<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GNN Frame Visualizations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            cursor: pointer;
            margin-right: 5px;
        }
        .tab.active {
            background-color: #fff;
            border-bottom: 1px solid #fff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .dataset-selector {
            margin: 20px 0;
        }
        .dataset-selector select {
            padding: 8px;
            width: 100%;
            max-width: 500px;
        }
        .frame-viewer {
            margin-top: 20px;
        }
        .frame-display {
            margin: 20px auto;
            max-width: 800px;
            border: 1px solid #ddd;
            padding: 10px;
        }
        .frame-display img {
            max-width: 100%;
            height: auto;
        }
        .frame-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 20px 0;
        }
        .frame-controls button {
            padding: 8px 15px;
            margin: 0 5px;
            cursor: pointer;
        }
        .frame-controls input {
            width: 60px;
            padding: 8px;
            margin: 0 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>GNN Frame Visualizations</h1>
    
    <div class="tabs">
        <div class="tab active" onclick="openTab(event, 'temporal-1')">Temporal 1</div>
        <div class="tab" onclick="openTab(event, 'temporal-3')">Temporal 3</div>
        <div class="tab" onclick="openTab(event, 'temporal-5')">Temporal 5</div>
    </div>
    
    <div id="temporal-1" class="tab-content active">
        <h2>Temporal Window 1</h2>
        
        <div class="dataset-selector">
            <label for="temporal_1-dataset-select">Select Dataset:</label>
            <select id="temporal_1-dataset-select" onchange="loadDataset('temporal_1', this.value)">
                <option value="">-- Select a dataset --</option>
        
                <option value="20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348">20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348</option>
            
                <option value="20250219_120022_20250219_120024_CPPS_vertical_20250219_120120">20250219_120022_20250219_120024_CPPS_vertical_20250219_120120</option>
            
                <option value="20250219_121042_20250219_121043_CPPS_vertical_20250219_121046">20250219_121042_20250219_121043_CPPS_vertical_20250219_121046</option>
            
                <option value="20250219_171846_20250219_171851_CPPS_vertical_20250219_171809">20250219_171846_20250219_171851_CPPS_vertical_20250219_171809</option>
            
                <option value="20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3">20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3</option>
            
                <option value="20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401">20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401</option>
            
                <option value="20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2">20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2</option>
            
                <option value="20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944">20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944</option>
            
                <option value="20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149">20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149</option>
            
                <option value="20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142">20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142</option>
            
                <option value="20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823">20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823</option>
            
                <option value="20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001">20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001</option>
            
                <option value="20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019">20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019</option>
            
                <option value="20250306_111722_20250306_111718_CPPS_vertical_20250306_111807">20250306_111722_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_112037_20250306_111718_CPPS_vertical_20250306_111807">20250306_112037_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_115421_20250306_115424_CPPS_vertical_20250306_115609">20250306_115421_20250306_115424_CPPS_vertical_20250306_115609</option>
            
                <option value="20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724">20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724</option>
            
                <option value="20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112">20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112</option>
            
                <option value="20250306_124624_20250306_124539_CPPS_vertical_20250306_124652">20250306_124624_20250306_124539_CPPS_vertical_20250306_124652</option>
            
                <option value="20250306_124834_20250306_124539_CPPS_vertical_20250306_124652">20250306_124834_20250306_124539_CPPS_vertical_20250306_124652</option>
            
            </select>
        </div>
        
        <div class="frame-viewer">
            <div id="temporal_1-frame-display" class="frame-display">
                <p>Select a dataset to view frames</p>
            </div>
            
            <div class="frame-controls">
                <button onclick="prevFrame()" id="temporal_1-prev-btn" disabled>Previous</button>
                <input type="number" id="temporal_1-frame-num" min="1" value="1" disabled>
                <span id="temporal_1-frame-count">/ 0</span>
                <button onclick="nextFrame()" id="temporal_1-next-btn" disabled>Next</button>
                <button onclick="playFrames()" id="temporal_1-play-btn" disabled>Play</button>
                <button onclick="stopFrames()" id="temporal_1-stop-btn" disabled>Stop</button>
            </div>
        </div>
    </div>
        
    <div id="temporal-3" class="tab-content">
        <h2>Temporal Window 3</h2>
        
        <div class="dataset-selector">
            <label for="temporal_3-dataset-select">Select Dataset:</label>
            <select id="temporal_3-dataset-select" onchange="loadDataset('temporal_3', this.value)">
                <option value="">-- Select a dataset --</option>
        
                <option value="20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348">20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348</option>
            
                <option value="20250219_120022_20250219_120024_CPPS_vertical_20250219_120120">20250219_120022_20250219_120024_CPPS_vertical_20250219_120120</option>
            
                <option value="20250219_121042_20250219_121043_CPPS_vertical_20250219_121046">20250219_121042_20250219_121043_CPPS_vertical_20250219_121046</option>
            
                <option value="20250219_171846_20250219_171851_CPPS_vertical_20250219_171809">20250219_171846_20250219_171851_CPPS_vertical_20250219_171809</option>
            
                <option value="20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3">20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3</option>
            
                <option value="20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401">20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401</option>
            
                <option value="20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2">20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2</option>
            
                <option value="20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944">20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944</option>
            
                <option value="20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149">20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149</option>
            
                <option value="20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142">20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142</option>
            
                <option value="20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823">20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823</option>
            
                <option value="20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001">20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001</option>
            
                <option value="20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019">20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019</option>
            
                <option value="20250306_111722_20250306_111718_CPPS_vertical_20250306_111807">20250306_111722_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_112037_20250306_111718_CPPS_vertical_20250306_111807">20250306_112037_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_115421_20250306_115424_CPPS_vertical_20250306_115609">20250306_115421_20250306_115424_CPPS_vertical_20250306_115609</option>
            
                <option value="20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724">20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724</option>
            
                <option value="20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112">20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112</option>
            
                <option value="20250306_124624_20250306_124539_CPPS_vertical_20250306_124652">20250306_124624_20250306_124539_CPPS_vertical_20250306_124652</option>
            
                <option value="20250306_124834_20250306_124539_CPPS_vertical_20250306_124652">20250306_124834_20250306_124539_CPPS_vertical_20250306_124652</option>
            
            </select>
        </div>
        
        <div class="frame-viewer">
            <div id="temporal_3-frame-display" class="frame-display">
                <p>Select a dataset to view frames</p>
            </div>
            
            <div class="frame-controls">
                <button onclick="prevFrame()" id="temporal_3-prev-btn" disabled>Previous</button>
                <input type="number" id="temporal_3-frame-num" min="1" value="1" disabled>
                <span id="temporal_3-frame-count">/ 0</span>
                <button onclick="nextFrame()" id="temporal_3-next-btn" disabled>Next</button>
                <button onclick="playFrames()" id="temporal_3-play-btn" disabled>Play</button>
                <button onclick="stopFrames()" id="temporal_3-stop-btn" disabled>Stop</button>
            </div>
        </div>
    </div>
        
    <div id="temporal-5" class="tab-content">
        <h2>Temporal Window 5</h2>
        
        <div class="dataset-selector">
            <label for="temporal_5-dataset-select">Select Dataset:</label>
            <select id="temporal_5-dataset-select" onchange="loadDataset('temporal_5', this.value)">
                <option value="">-- Select a dataset --</option>
        
                <option value="20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348">20250219_113405_20250219_113406_CPPS_horizontal_20250219_113348</option>
            
                <option value="20250219_120022_20250219_120024_CPPS_vertical_20250219_120120">20250219_120022_20250219_120024_CPPS_vertical_20250219_120120</option>
            
                <option value="20250219_121042_20250219_121043_CPPS_vertical_20250219_121046">20250219_121042_20250219_121043_CPPS_vertical_20250219_121046</option>
            
                <option value="20250219_171846_20250219_171851_CPPS_vertical_20250219_171809">20250219_171846_20250219_171851_CPPS_vertical_20250219_171809</option>
            
                <option value="20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3">20250219_174928_20250219_174930_CPPS_diagonal_20250219_174909_v3</option>
            
                <option value="20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401">20250219_180412_20250219_180415_CPPS_horizontal_20250219_180401</option>
            
                <option value="20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2">20250221_104556_20250221_104601_CPPS_diagonal_20250221_104617_v2</option>
            
                <option value="20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944">20250221_111954_half_v2_20250221_111950_half_v2_CPPS_horizontal_v2_20250221_111944</option>
            
                <option value="20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149">20250221_120046_v2_20250221_120054_v2_CPPS_horizontal_v2_20250221_120149</option>
            
                <option value="20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142">20250221_121015_20250221_121118_CPPS_horizontal_robot_1_vertical_robot_2_20250221_121142</option>
            
                <option value="20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823">20250221_122706_20250221_122703_CPPS_vertical_robot_1_horizontal_robot_2_20250221_122823</option>
            
                <option value="20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001">20250221_124833_20250221_124828_CPPS_horizontal_robot_1_diagonal_robot_2_v2_20250221_125001</option>
            
                <option value="20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019">20250221_125918_20250221_125920_CPPS_diagonal_robot_1_horizontal_robot_2_20250221_130019</option>
            
                <option value="20250306_111722_20250306_111718_CPPS_vertical_20250306_111807">20250306_111722_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_112037_20250306_111718_CPPS_vertical_20250306_111807">20250306_112037_20250306_111718_CPPS_vertical_20250306_111807</option>
            
                <option value="20250306_115421_20250306_115424_CPPS_vertical_20250306_115609">20250306_115421_20250306_115424_CPPS_vertical_20250306_115609</option>
            
                <option value="20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724">20250306_120629_20250306_120624_CPPS_diagonal_horizontal_20250306_120724</option>
            
                <option value="20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112">20250306_123036_20250306_123030_CPPS_horizontal_vertical_20250306_123112</option>
            
                <option value="20250306_124624_20250306_124539_CPPS_vertical_20250306_124652">20250306_124624_20250306_124539_CPPS_vertical_20250306_124652</option>
            
                <option value="20250306_124834_20250306_124539_CPPS_vertical_20250306_124652">20250306_124834_20250306_124539_CPPS_vertical_20250306_124652</option>
            
            </select>
        </div>
        
        <div class="frame-viewer">
            <div id="temporal_5-frame-display" class="frame-display">
                <p>Select a dataset to view frames</p>
            </div>
            
            <div class="frame-controls">
                <button onclick="prevFrame()" id="temporal_5-prev-btn" disabled>Previous</button>
                <input type="number" id="temporal_5-frame-num" min="1" value="1" disabled>
                <span id="temporal_5-frame-count">/ 0</span>
                <button onclick="nextFrame()" id="temporal_5-next-btn" disabled>Next</button>
                <button onclick="playFrames()" id="temporal_5-play-btn" disabled>Play</button>
                <button onclick="stopFrames()" id="temporal_5-stop-btn" disabled>Stop</button>
            </div>
        </div>
    </div>
        
    <script>
        // Global variables
        let currentTemporal = 'temporal_1';
        let currentDataset = '';
        let currentFrame = 1;
        let totalFrames = 0;
        let isPlaying = false;
        let playInterval = null;
        
        function openTab(evt, tabName) {
            var i, tabContent, tabLinks;
            
            // Hide all tab content
            tabContent = document.getElementsByClassName("tab-content");
            for (i = 0; i < tabContent.length; i++) {
                tabContent[i].className = tabContent[i].className.replace(" active", "");
            }
            
            // Remove "active" class from all tabs
            tabLinks = document.getElementsByClassName("tab");
            for (i = 0; i < tabLinks.length; i++) {
                tabLinks[i].className = tabLinks[i].className.replace(" active", "");
            }
            
            // Show the current tab and add "active" class to the button that opened the tab
            document.getElementById(tabName).className += " active";
            evt.currentTarget.className += " active";
        }
        
        function loadDataset(temporal, dataset) {
            if (dataset === '') return;
            
            currentTemporal = temporal;
            currentDataset = dataset;
            currentFrame = 1;
            
            // Enable controls
            document.getElementById(`${currentTemporal}-prev-btn`).disabled = false;
            document.getElementById(`${currentTemporal}-next-btn`).disabled = false;
            document.getElementById(`${currentTemporal}-play-btn`).disabled = false;
            document.getElementById(`${currentTemporal}-stop-btn`).disabled = false;
            document.getElementById(`${currentTemporal}-frame-num`).disabled = false;
            
            // Get the frame count
            fetch(`${currentTemporal}/${currentDataset}/frame_count.txt`)
                .then(response => response.text())
                .then(count => {
                    totalFrames = parseInt(count);
                    document.getElementById(`${currentTemporal}-frame-count`).textContent = `/ ${totalFrames}`;
                    loadFrame();
                })
                .catch(error => {
                    console.error('Error loading frame count:', error);
                    totalFrames = 0;
                    document.getElementById(`${currentTemporal}-frame-count`).textContent = `/ 0`;
                });
        }
        
        function loadFrame() {
            if (currentFrame < 1) currentFrame = 1;
            if (currentFrame > totalFrames) currentFrame = totalFrames;
            
            const frameDisplay = document.getElementById(`${currentTemporal}-frame-display`);
            const frameNum = document.getElementById(`${currentTemporal}-frame-num`);
            
            // Update frame number input
            frameNum.value = currentFrame;
            
            // Display the frame
            frameDisplay.innerHTML = `<img src="${currentTemporal}/${currentDataset}/frame_${String(currentFrame).padStart(4, '0')}.png" alt="Frame ${currentFrame}">`;
        }
        
        function prevFrame() {
            if (currentFrame > 1) {
                currentFrame--;
                loadFrame();
            }
        }
        
        function nextFrame() {
            if (currentFrame < totalFrames) {
                currentFrame++;
                loadFrame();
            }
        }
        
        function playFrames() {
            if (!isPlaying) {
                isPlaying = true;
                playInterval = setInterval(() => {
                    if (currentFrame < totalFrames) {
                        currentFrame++;
                        loadFrame();
                    } else {
                        stopFrames();
                    }
                }, 200); // Change frame every 200ms
            }
        }
        
        function stopFrames() {
            if (isPlaying) {
                isPlaying = false;
                clearInterval(playInterval);
            }
        }
        
        // Add event listener for frame number input
        document.addEventListener('DOMContentLoaded', function() {
            const frameNumInputs = document.querySelectorAll('[id$="-frame-num"]');
            frameNumInputs.forEach(input => {
                input.addEventListener('change', function() {
                    const temporal = this.id.replace('-frame-num', '');
                    if (temporal === currentTemporal) {
                        let newFrame = parseInt(this.value);
                        if (newFrame < 1) newFrame = 1;
                        if (newFrame > totalFrames) newFrame = totalFrames;
                        currentFrame = newFrame;
                        loadFrame();
                    }
                });
            });
        });
    </script>
</body>
</html>
    