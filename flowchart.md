::: mermaid
flowchart TD
    subgraph Phase1 ["📊 Phase I: Data Collection"]
        A[Multi-Robot Radar Sensors]
        B[Vicon Motion Capture]
        C[Environment Metadata]
    end
    
    subgraph Phase2 ["🔧 Phase II: Preprocessing"]
        D1[Stage 1: Raw Data Extraction]
        D2[Stage 2: Multi-Modal Sync]
        D3[Stage 3: Coordinate Transform]
        D4[Stage 4: Quality Enhancement]
        D5[Stage 5: Semantic Annotation]
        D6[Stage 6: Graph Generation]
        D7[Stage 7: Dataset Organization]
        
        D1 --> D2 --> D3 --> D4 --> D5 --> D6 --> D7
    end
    
    subgraph Phase3 ["🏗️ Phase III: Model Development"]
        direction LR
        E1[GraphSAGE<br/>15K params]
        E2[GATv2 Variants<br/>25K-6M params]
        E3[ECC Novel<br/>2M-50M params]
    end
    
    subgraph Phase4 ["🎓 Phase IV: Training"]
        F1[Hardware Setup]
        F2[Optimization Framework]
        F3[19.5h Total Training]
        F1 --> F2 --> F3
    end
    
    subgraph Phase5 ["📈 Phase V: Evaluation"]
        direction LR
        G1[Classification Metrics]
        G2[Regression Metrics]
        G3[Efficiency Analysis]
        G4[Spatial Analysis]
    end
    
    subgraph Phase6 ["🎯 Phase VI: Results"]
        H1[Novel ECC Architecture]
        H2[7-Stage Pipeline]
        H3[Parameter Efficiency]
        H4[Production Guidelines]
    end
    
    %% Main flow
    Phase1 --> Phase2
    Phase2 --> Phase3
    Phase3 --> Phase4
    Phase4 --> Phase5
    Phase5 --> Phase6
    
    %% Quality assurance
    QA[Quality Assurance]
    QA -.-> Phase2
    QA -.-> Phase3
    QA -.-> Phase5
    
    %% Temporal configuration
    TW[Temporal Windows<br/>3-Frame vs 5-Frame]
    TW -.-> Phase3
    
    %% Parameter efficiency
    PE[Parameter Efficiency<br/>Analysis]
    PE -.-> Phase4
    PE -.-> Phase5

    %% Styling
    classDef phaseBox fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef stageBox fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000
    classDef modelBox fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000
    classDef resultBox fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef qaBox fill:#ffebee,stroke:#d32f2f,stroke-width:1px,stroke-dasharray:5 5,color:#000
    
    class Phase1,Phase2,Phase3,Phase4,Phase5,Phase6 phaseBox
    class D1,D2,D3,D4,D5,D6,D7,F1,F2,F3 stageBox
    class E1,E2,E3,G1,G2,G3,G4 modelBox
    class H1,H2,H3,H4 resultBox
    class QA,TW,PE qaBox
:::