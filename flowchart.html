<!DOCTYPE html><html><head>
      <title>flowchart</title>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      
      <link rel="stylesheet" href="file:////home/<USER>/.vscode/extensions/shd101wyy.markdown-preview-enhanced-0.8.18/crossnote/dependencies/katex/katex.min.css">
      
      
      
      
      
      <style>
      code[class*=language-],pre[class*=language-]{color:#333;background:0 0;font-family:Consolas,"Liberation Mono",Menlo,Courier,monospace;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.4;-moz-tab-size:8;-o-tab-size:8;tab-size:8;-webkit-hyphens:none;-moz-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:.8em;overflow:auto;border-radius:3px;background:#f5f5f5}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal;background:#f5f5f5}.token.blockquote,.token.comment{color:#969896}.token.cdata{color:#183691}.token.doctype,.token.macro.property,.token.punctuation,.token.variable{color:#333}.token.builtin,.token.important,.token.keyword,.token.operator,.token.rule{color:#a71d5d}.token.attr-value,.token.regex,.token.string,.token.url{color:#183691}.token.atrule,.token.boolean,.token.code,.token.command,.token.constant,.token.entity,.token.number,.token.property,.token.symbol{color:#0086b3}.token.prolog,.token.selector,.token.tag{color:#63a35c}.token.attr-name,.token.class,.token.class-name,.token.function,.token.id,.token.namespace,.token.pseudo-class,.token.pseudo-element,.token.url-reference .token.variable{color:#795da3}.token.entity{cursor:help}.token.title,.token.title .token.punctuation{font-weight:700;color:#1d3e81}.token.list{color:#ed6a43}.token.inserted{background-color:#eaffea;color:#55a532}.token.deleted{background-color:#ffecec;color:#bd2c00}.token.bold{font-weight:700}.token.italic{font-style:italic}.language-json .token.property{color:#183691}.language-markup .token.tag .token.punctuation{color:#333}.language-css .token.function,code.language-css{color:#0086b3}.language-yaml .token.atrule{color:#63a35c}code.language-yaml{color:#183691}.language-ruby .token.function{color:#333}.language-markdown .token.url{color:#795da3}.language-makefile .token.symbol{color:#795da3}.language-makefile .token.variable{color:#183691}.language-makefile .token.builtin{color:#0086b3}.language-bash .token.keyword{color:#0086b3}pre[data-line]{position:relative;padding:1em 0 1em 3em}pre[data-line] .line-highlight-wrapper{position:absolute;top:0;left:0;background-color:transparent;display:block;width:100%}pre[data-line] .line-highlight{position:absolute;left:0;right:0;padding:inherit 0;margin-top:1em;background:hsla(24,20%,50%,.08);background:linear-gradient(to right,hsla(24,20%,50%,.1) 70%,hsla(24,20%,50%,0));pointer-events:none;line-height:inherit;white-space:pre}pre[data-line] .line-highlight:before,pre[data-line] .line-highlight[data-end]:after{content:attr(data-start);position:absolute;top:.4em;left:.6em;min-width:1em;padding:0 .5em;background-color:hsla(24,20%,50%,.4);color:#f4f1ef;font:bold 65%/1.5 sans-serif;text-align:center;vertical-align:.3em;border-radius:999px;text-shadow:none;box-shadow:0 1px #fff}pre[data-line] .line-highlight[data-end]:after{content:attr(data-end);top:auto;bottom:.4em}html body{font-family:'Helvetica Neue',Helvetica,'Segoe UI',Arial,freesans,sans-serif;font-size:16px;line-height:1.6;color:#333;background-color:#fff;overflow:initial;box-sizing:border-box;word-wrap:break-word}html body>:first-child{margin-top:0}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{line-height:1.2;margin-top:1em;margin-bottom:16px;color:#000}html body h1{font-size:2.25em;font-weight:300;padding-bottom:.3em}html body h2{font-size:1.75em;font-weight:400;padding-bottom:.3em}html body h3{font-size:1.5em;font-weight:500}html body h4{font-size:1.25em;font-weight:600}html body h5{font-size:1.1em;font-weight:600}html body h6{font-size:1em;font-weight:600}html body h1,html body h2,html body h3,html body h4,html body h5{font-weight:600}html body h5{font-size:1em}html body h6{color:#5c5c5c}html body strong{color:#000}html body del{color:#5c5c5c}html body a:not([href]){color:inherit;text-decoration:none}html body a{color:#08c;text-decoration:none}html body a:hover{color:#00a3f5;text-decoration:none}html body img{max-width:100%}html body>p{margin-top:0;margin-bottom:16px;word-wrap:break-word}html body>ol,html body>ul{margin-bottom:16px}html body ol,html body ul{padding-left:2em}html body ol.no-list,html body ul.no-list{padding:0;list-style-type:none}html body ol ol,html body ol ul,html body ul ol,html body ul ul{margin-top:0;margin-bottom:0}html body li{margin-bottom:0}html body li.task-list-item{list-style:none}html body li>p{margin-top:0;margin-bottom:0}html body .task-list-item-checkbox{margin:0 .2em .25em -1.8em;vertical-align:middle}html body .task-list-item-checkbox:hover{cursor:pointer}html body blockquote{margin:16px 0;font-size:inherit;padding:0 15px;color:#5c5c5c;background-color:#f0f0f0;border-left:4px solid #d6d6d6}html body blockquote>:first-child{margin-top:0}html body blockquote>:last-child{margin-bottom:0}html body hr{height:4px;margin:32px 0;background-color:#d6d6d6;border:0 none}html body table{margin:10px 0 15px 0;border-collapse:collapse;border-spacing:0;display:block;width:100%;overflow:auto;word-break:normal;word-break:keep-all}html body table th{font-weight:700;color:#000}html body table td,html body table th{border:1px solid #d6d6d6;padding:6px 13px}html body dl{padding:0}html body dl dt{padding:0;margin-top:16px;font-size:1em;font-style:italic;font-weight:700}html body dl dd{padding:0 16px;margin-bottom:16px}html body code{font-family:Menlo,Monaco,Consolas,'Courier New',monospace;font-size:.85em;color:#000;background-color:#f0f0f0;border-radius:3px;padding:.2em 0}html body code::after,html body code::before{letter-spacing:-.2em;content:'\00a0'}html body pre>code{padding:0;margin:0;word-break:normal;white-space:pre;background:0 0;border:0}html body .highlight{margin-bottom:16px}html body .highlight pre,html body pre{padding:1em;overflow:auto;line-height:1.45;border:#d6d6d6;border-radius:3px}html body .highlight pre{margin-bottom:0;word-break:normal}html body pre code,html body pre tt{display:inline;max-width:initial;padding:0;margin:0;overflow:initial;line-height:inherit;word-wrap:normal;background-color:transparent;border:0}html body pre code:after,html body pre code:before,html body pre tt:after,html body pre tt:before{content:normal}html body blockquote,html body dl,html body ol,html body p,html body pre,html body ul{margin-top:0;margin-bottom:16px}html body kbd{color:#000;border:1px solid #d6d6d6;border-bottom:2px solid #c7c7c7;padding:2px 4px;background-color:#f0f0f0;border-radius:3px}@media print{html body{background-color:#fff}html body h1,html body h2,html body h3,html body h4,html body h5,html body h6{color:#000;page-break-after:avoid}html body blockquote{color:#5c5c5c}html body pre{page-break-inside:avoid}html body table{display:table}html body img{display:block;max-width:100%;max-height:100%}html body code,html body pre{word-wrap:break-word;white-space:pre}}.markdown-preview{width:100%;height:100%;box-sizing:border-box}.markdown-preview ul{list-style:disc}.markdown-preview ul ul{list-style:circle}.markdown-preview ul ul ul{list-style:square}.markdown-preview ol{list-style:decimal}.markdown-preview ol ol,.markdown-preview ul ol{list-style-type:lower-roman}.markdown-preview ol ol ol,.markdown-preview ol ul ol,.markdown-preview ul ol ol,.markdown-preview ul ul ol{list-style-type:lower-alpha}.markdown-preview .newpage,.markdown-preview .pagebreak{page-break-before:always}.markdown-preview pre.line-numbers{position:relative;padding-left:3.8em;counter-reset:linenumber}.markdown-preview pre.line-numbers>code{position:relative}.markdown-preview pre.line-numbers .line-numbers-rows{position:absolute;pointer-events:none;top:1em;font-size:100%;left:0;width:3em;letter-spacing:-1px;border-right:1px solid #999;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.markdown-preview pre.line-numbers .line-numbers-rows>span{pointer-events:none;display:block;counter-increment:linenumber}.markdown-preview pre.line-numbers .line-numbers-rows>span:before{content:counter(linenumber);color:#999;display:block;padding-right:.8em;text-align:right}.markdown-preview .mathjax-exps .MathJax_Display{text-align:center!important}.markdown-preview:not([data-for=preview]) .code-chunk .code-chunk-btn-group{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .status{display:none}.markdown-preview:not([data-for=preview]) .code-chunk .output-div{margin-bottom:16px}.markdown-preview .md-toc{padding:0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link div,.markdown-preview .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}.markdown-preview .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}.scrollbar-style::-webkit-scrollbar{width:8px}.scrollbar-style::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}.scrollbar-style::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode]){position:relative;width:100%;height:100%;top:0;left:0;margin:0;padding:0;overflow:auto}html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{position:relative;top:0;min-height:100vh}@media screen and (min-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em calc(50% - 457px + 2em)}}@media screen and (max-width:914px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode]) .markdown-preview{font-size:14px!important;padding:1em}}@media print{html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{display:none}}html body[for=html-export]:not([data-presentation-mode]) #sidebar-toc-btn{position:fixed;bottom:8px;left:8px;font-size:28px;cursor:pointer;color:inherit;z-index:99;width:32px;text-align:center;opacity:.4}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] #sidebar-toc-btn{opacity:1}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc{position:fixed;top:0;left:0;width:300px;height:100%;padding:32px 0 48px 0;font-size:14px;box-shadow:0 0 4px rgba(150,150,150,.33);box-sizing:border-box;overflow:auto;background-color:inherit}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar{width:8px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-track{border-radius:10px;background-color:transparent}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc::-webkit-scrollbar-thumb{border-radius:5px;background-color:rgba(150,150,150,.66);border:4px solid rgba(150,150,150,.66);background-clip:content-box}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc a{text-decoration:none}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc{padding:0 16px}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link{display:inline;padding:.25rem 0}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link div,html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper .md-toc-link p{display:inline}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .md-sidebar-toc .md-toc .md-toc-link-wrapper.highlighted .md-toc-link{font-weight:800}html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{left:300px;width:calc(100% - 300px);padding:2em calc(50% - 457px - 300px / 2);margin:0;box-sizing:border-box}@media screen and (max-width:1274px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{padding:2em}}@media screen and (max-width:450px){html body[for=html-export]:not([data-presentation-mode])[html-show-sidebar-toc] .markdown-preview{width:100%}}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .markdown-preview{left:50%;transform:translateX(-50%)}html body[for=html-export]:not([data-presentation-mode]):not([html-show-sidebar-toc]) .md-sidebar-toc{display:none}
/* Please visit the URL below for more information: */
/*   https://shd101wyy.github.io/markdown-preview-enhanced/#/customize-css */

      </style>
      <!-- The content below will be included at the end of the <head> element. --><script type="text/javascript">
  document.addEventListener("DOMContentLoaded", function () {
    // your code here
  });
</script></head><body for="html-export">
    
    
      <div class="crossnote markdown-preview  ">
      
<p>flowchart TD<br>
A[Research Methodology Overview<br>Graph Neural Networks for<br>Collaborative Robot Occupancy Prediction] --&gt; B[PHASE I: DATA COLLECTION]</p>
<pre class="language-text">B --&gt; B1[Multi-Modal Sensor Data Acquisition]
B1 --&gt; B2[Two autonomous robots with radar sensors]
B1 --&gt; B3[Vicon motion capture system ground truth]
B1 --&gt; B4[Controlled warehouse environment&lt;br/&gt;with collaborative scenarios]

B --&gt; C[PHASE II: DATA PREPROCESSING]

C --&gt; C1[Seven-Stage Processing Pipeline]

C1 --&gt; D1[Stage 1: Raw Data&lt;br/&gt;Extraction &amp; Standardization]
C1 --&gt; D2[Stage 2: Multi-Modal&lt;br/&gt;Synchronization]
C1 --&gt; D3[Stage 3: Coordinate&lt;br/&gt;Transformation]
C1 --&gt; D4[Stage 4: Data Quality&lt;br/&gt;Enhancement]
C1 --&gt; D5[Stage 5: Semantic&lt;br/&gt;Annotation]
C1 --&gt; D6[Stage 6: Graph Structure&lt;br/&gt;Generation]
C1 --&gt; D7[Stage 7: Dataset&lt;br/&gt;Organization]

D1 --&gt; D1A[• Vicon parsing&lt;br/&gt;• Radar extraction&lt;br/&gt;• Format conversion]
D2 --&gt; D2A[• Temporal alignment&lt;br/&gt;• Motion detection&lt;br/&gt;• Cross-sensor consistency]
D3 --&gt; D3A[• Global frame definition&lt;br/&gt;• Sensor calibration&lt;br/&gt;• Accuracy validation]
D4 --&gt; D4A[• Spatial filtering&lt;br/&gt;• SNR filtering&lt;br/&gt;• Outlier removal]
D5 --&gt; D5A[• Workstation detection&lt;br/&gt;• Robot tracking&lt;br/&gt;• Boundary classification]
D6 --&gt; D6A[• Voxelization&lt;br/&gt;• Node features&lt;br/&gt;• Edge connectivity&lt;br/&gt;• Temporal integration]
D7 --&gt; D7A[• Train/Val/Test partitioning&lt;br/&gt;• Temporal integrity&lt;br/&gt;• Statistical balance]

C --&gt; E[PHASE III: MODEL DEVELOPMENT]

E --&gt; E1[Graph Neural Network Architectures]

E1 --&gt; F1[GraphSAGE Baseline]
E1 --&gt; F2[GATv2 Variants]
E1 --&gt; F3[ECC Novel Variants]

F1 --&gt; F1A[• Historical data model 15K params&lt;br/&gt;• Simple aggregation&lt;br/&gt;• Proven baseline]

F2 --&gt; F2A[• Standard 25K&lt;br/&gt;• Complex 169K&lt;br/&gt;• Enhanced 6M&lt;br/&gt;• 5-Layer 52K]
F2A --&gt; F2B[• Multi-head attention&lt;br/&gt;• Layer normalization&lt;br/&gt;• Skip connections]

F3 --&gt; F3A[• Temporal 3 50M&lt;br/&gt;• Temporal 5 2M&lt;br/&gt;• Hybrid 16M]
F3A --&gt; F3B[• Edge-conditioned message passing&lt;br/&gt;• Spatial reasoning&lt;br/&gt;• Regression capability]

E1 --&gt; G[Temporal Window Configurations]
G --&gt; G1[3-Frame Window&lt;br/&gt;• Focused context&lt;br/&gt;• Higher accuracy]
G --&gt; G2[5-Frame Window&lt;br/&gt;• Extended context&lt;br/&gt;• Motion patterns]

E --&gt; H[PHASE IV: TRAINING &amp; OPTIMIZATION]

H --&gt; H1[Training Framework]

H1 --&gt; I1[Hardware Setup]
H1 --&gt; I2[Optimization]
H1 --&gt; I3[Regularization]
H1 --&gt; I4[Monitoring]

I1 --&gt; I1A[• CUDA GPU&lt;br/&gt;• PyTorch Geometric&lt;br/&gt;• Batch processing]
I2 --&gt; I2A[• Adam/AdamW&lt;br/&gt;• Learning rate scheduling&lt;br/&gt;• Gradient clipping]
I3 --&gt; I3A[• Dropout&lt;br/&gt;• Batch norm&lt;br/&gt;• Weight decay&lt;br/&gt;• Layer norm]
I4 --&gt; I4A[• Loss tracking&lt;br/&gt;• Early stopping&lt;br/&gt;• Validation&lt;br/&gt;• Performance metrics]

H --&gt; J[PHASE V: EVALUATION &amp; ANALYSIS]

J --&gt; J1[Comprehensive Evaluation Framework]

J1 --&gt; K1[Classification Metrics]
J1 --&gt; K2[Regression Metrics]
J1 --&gt; K3[Efficiency Metrics]
J1 --&gt; K4[Spatial Analysis Metrics]

K1 --&gt; K1A[• Accuracy&lt;br/&gt;• Precision&lt;br/&gt;• Recall&lt;br/&gt;• F1-Score&lt;br/&gt;• ROC AUC]
K2 --&gt; K2A[• R² Score&lt;br/&gt;• MSE/RMSE&lt;br/&gt;• MAE&lt;br/&gt;• Explained variance]
K3 --&gt; K3A[• Parameter efficiency&lt;br/&gt;• Training time&lt;br/&gt;• Memory usage&lt;br/&gt;• Inference speed]
K4 --&gt; K4A[• IoU&lt;br/&gt;• Spatial coverage&lt;br/&gt;• Cross-robot consistency]

J1 --&gt; L[Statistical Analysis &amp; Visualization]
L --&gt; L1[Comparative Analysis&lt;br/&gt;• Model ranking&lt;br/&gt;• Statistical testing&lt;br/&gt;• Cross-validation]
L --&gt; L2[Performance Insights&lt;br/&gt;• Architecture trade-offs&lt;br/&gt;• Scalability assessment&lt;br/&gt;• Deployment guidelines]

J --&gt; M[RESEARCH CONTRIBUTIONS]

M --&gt; M1[• Comprehensive GNN architecture comparison for collaborative robotics]
M --&gt; M2[• Novel Edge-Conditioned Convolution application with regression capabilities]
M --&gt; M3[• Seven-stage preprocessing pipeline with quality assurance framework]
M --&gt; M4[• Parameter efficiency analysis across 15K-50M parameter range]
M --&gt; M5[• Temporal modeling framework for multi-robot occupancy prediction]
M --&gt; M6[• Production-ready deployment guidelines and performance benchmarks]

style A fill:#e1f5fe
style B fill:#f3e5f5
style C fill:#e8f5e8
style E fill:#fff3e0
style H fill:#fce4ec
style J fill:#f1f8e9
style M fill:#e0f2f1

classDef phaseBox fill:#ffffff,stroke:#333,stroke-width:2px
classDef stageBox fill:#f5f5f5,stroke:#666,stroke-width:1px
classDef detailBox fill:#fafafa,stroke:#999,stroke-width:1px

class B,C,E,H,J,M phaseBox
class B1,C1,E1,H1,J1,L stageBox
class D1,D2,D3,D4,D5,D6,D7,F1,F2,F3,G,I1,I2,I3,I4,K1,K2,K3,K4,L1,L2 detailBox
</pre>

      </div>
      
      
    
    
    
    
    
    
  
    </body></html>