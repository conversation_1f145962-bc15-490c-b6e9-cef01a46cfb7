#!/usr/bin/env python3

import os
import argparse
import pandas as pd
import numpy as np
import torch
from torch_geometric.data import Data
import glob
from tqdm import tqdm
import time
import csv

# Define label mapping
LABEL_MAPPING = {
    'unknown': 0,
    'workstation': 1,
    'robot': 2,
    'boundary': 3
}

def create_gnn_frame(df, timestamp, voxel_size=0.1):
    """
    Create a GNN frame from a DataFrame of points at a specific timestamp.
    
    Args:
        df: DataFrame containing point cloud data
        timestamp: Timestamp for the frame
        voxel_size: Size of voxels for discretizing the point cloud
        
    Returns:
        PyTorch Geometric Data object
    """
    # Extract positions and labels
    positions = df[['robot_1_global_x_radar', 'robot_1_global_y_radar', 'robot_1_global_z_radar']].values
    
    # Get annotations
    annotations = df['annotation_general'].values
    
    # Convert string annotations to numeric labels
    labels = np.zeros(len(annotations), dtype=np.int64)
    for i, annotation in enumerate(annotations):
        labels[i] = LABEL_MAPPING.get(annotation, 0)  # Default to 0 (unknown) if annotation not found
    
    # Voxelize the point cloud
    voxel_indices = np.floor(positions / voxel_size).astype(np.int64)
    
    # Create a dictionary to store voxel data
    voxels = {}
    
    # Assign points to voxels and compute voxel features
    for i, (pos, label) in enumerate(zip(positions, labels)):
        voxel_idx = tuple(voxel_indices[i])
        
        if voxel_idx not in voxels:
            voxels[voxel_idx] = {
                'positions': [],
                'labels': []
            }
        
        voxels[voxel_idx]['positions'].append(pos)
        voxels[voxel_idx]['labels'].append(label)
    
    # Compute voxel features and labels
    voxel_positions = []
    voxel_labels = []
    
    for voxel_idx, voxel_data in voxels.items():
        # Compute voxel center (mean position of all points in the voxel)
        voxel_pos = np.mean(voxel_data['positions'], axis=0)
        voxel_positions.append(voxel_pos)
        
        # Assign label based on majority vote
        labels = np.array(voxel_data['labels'])
        unique_labels, counts = np.unique(labels, return_counts=True)
        voxel_label = unique_labels[np.argmax(counts)]
        voxel_labels.append(voxel_label)
    
    # Convert to numpy arrays
    voxel_positions = np.array(voxel_positions)
    voxel_labels = np.array(voxel_labels)
    
    # Create node features (13 features per node)
    num_nodes = len(voxel_positions)
    node_features = np.zeros((num_nodes, 13))
    
    # Feature 0-2: Normalized position (x, y, z)
    pos_min = np.min(voxel_positions, axis=0)
    pos_max = np.max(voxel_positions, axis=0)
    pos_range = pos_max - pos_min
    pos_range[pos_range == 0] = 1  # Avoid division by zero
    node_features[:, 0:3] = (voxel_positions - pos_min) / pos_range
    
    # Feature 3-5: Raw position (x, y, z)
    node_features[:, 3:6] = voxel_positions
    
    # Feature 6-8: Position relative to center (x, y, z)
    center = np.mean(voxel_positions, axis=0)
    node_features[:, 6:9] = voxel_positions - center
    
    # Feature 9-11: Position relative to origin (x, y, z)
    node_features[:, 9:12] = voxel_positions
    
    # Feature 12: Distance to center
    node_features[:, 12] = np.linalg.norm(voxel_positions - center, axis=1)
    
    # Create edges (fully connected graph)
    edge_index = []
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j:  # Exclude self-loops
                edge_index.append([i, j])
    
    # Convert to PyTorch tensors
    x = torch.tensor(node_features, dtype=torch.float)
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    y = torch.tensor(voxel_labels, dtype=torch.long)
    pos = torch.tensor(voxel_positions, dtype=torch.float)
    
    # Create PyTorch Geometric Data object
    data = Data(x=x, edge_index=edge_index, y=y, pos=pos)
    
    # Add timestamp
    data.timestamp = timestamp
    
    return data

def create_temporal_frame(frames, window_size, center_idx):
    """
    Create a temporal frame by aggregating multiple frames.
    
    Args:
        frames: List of frames
        window_size: Size of the temporal window
        center_idx: Index of the center frame
        
    Returns:
        PyTorch Geometric Data object
    """
    # Get the center frame
    center_frame = frames[center_idx]
    
    # Get the frames in the window
    start_idx = max(0, center_idx - window_size // 2)
    end_idx = min(len(frames), center_idx + window_size // 2 + 1)
    window_frames = frames[start_idx:end_idx]
    
    # Extract node features, positions, and labels from all frames in the window
    all_x = []
    all_pos = []
    all_y = []
    all_temporal_offsets = []
    
    for i, frame in enumerate(window_frames):
        # Calculate temporal offset
        temporal_offset = i - (center_idx - start_idx)
        
        # Add temporal offset to node features
        x_with_offset = torch.cat([frame.x, torch.full((frame.num_nodes, 1), temporal_offset, dtype=torch.float)], dim=1)
        
        all_x.append(x_with_offset)
        all_pos.append(frame.pos)
        all_y.append(frame.y)
        all_temporal_offsets.append(torch.full((frame.num_nodes,), temporal_offset, dtype=torch.float))
    
    # Concatenate node features, positions, and labels
    x = torch.cat(all_x, dim=0)
    pos = torch.cat(all_pos, dim=0)
    y = torch.cat(all_y, dim=0)
    
    # Create edges (fully connected graph)
    num_nodes = x.size(0)
    edge_index = []
    for i in range(num_nodes):
        for j in range(num_nodes):
            if i != j:  # Exclude self-loops
                edge_index.append([i, j])
    
    edge_index = torch.tensor(edge_index, dtype=torch.long).t().contiguous()
    
    # Create PyTorch Geometric Data object
    data = Data(x=x, edge_index=edge_index, y=y, pos=pos)
    
    # Add timestamp
    data.timestamp = center_frame.timestamp
    
    return data

def process_csv_file(csv_file, output_dir, voxel_size=0.1, temporal_windows=[1, 3, 5]):
    """
    Process a CSV file and create GNN frames.
    
    Args:
        csv_file: Path to the CSV file
        output_dir: Directory to save the GNN frames
        voxel_size: Size of voxels for discretizing the point cloud
        temporal_windows: List of temporal window sizes
    """
    try:
        # Get dataset name from file path
        dataset_path = os.path.dirname(csv_file)
        dataset_name = os.path.basename(dataset_path)
        
        # Create output directory for this dataset
        dataset_output_dir = os.path.join(output_dir, dataset_name)
        os.makedirs(dataset_output_dir, exist_ok=True)
        
        # Create directories for each temporal window
        for window_size in temporal_windows:
            os.makedirs(os.path.join(dataset_output_dir, f"temporal_{window_size}"), exist_ok=True)
        
        # Read CSV file
        df = pd.read_csv(csv_file)
        
        # Group by timestamp
        grouped = df.groupby('vicon_timestamp')
        
        # Create frames for each timestamp
        frames = []
        timestamps = []
        
        for timestamp, group in tqdm(grouped, desc=f"Processing {dataset_name}"):
            # Create GNN frame
            frame = create_gnn_frame(group, timestamp, voxel_size)
            
            # Add to list
            frames.append(frame)
            timestamps.append(timestamp)
        
        # Save non-temporal frames (window size 1)
        for i, (frame, timestamp) in enumerate(zip(frames, timestamps)):
            # Save frame
            output_path = os.path.join(dataset_output_dir, f"temporal_1", f"{timestamp}.pt")
            torch.save(frame, output_path)
        
        # Create and save temporal frames
        for window_size in [3, 5]:
            if window_size not in temporal_windows:
                continue
                
            for i in range(len(frames)):
                # Skip if not enough frames for the window
                if i < window_size // 2 or i >= len(frames) - window_size // 2:
                    continue
                
                # Create temporal frame
                temporal_frame = create_temporal_frame(frames, window_size, i)
                
                # Save frame
                output_path = os.path.join(dataset_output_dir, f"temporal_{window_size}", f"{timestamps[i]}.pt")
                torch.save(temporal_frame, output_path)
        
        return True
        
    except Exception as e:
        print(f"Error processing {csv_file}: {e}")
        return False

def collect_statistics(output_dir):
    """
    Collect statistics about the generated frames.
    
    Args:
        output_dir: Directory containing the GNN frames
    """
    # Initialize statistics
    total_frames = 0
    total_nodes = 0
    total_edges = 0
    label_counts = {0: 0, 1: 0, 2: 0, 3: 0}
    
    # Find all .pt files
    pt_files = []
    for root, dirs, files in os.walk(output_dir):
        for file in files:
            if file.endswith('.pt'):
                pt_files.append(os.path.join(root, file))
    
    # Process each .pt file
    for pt_file in tqdm(pt_files, desc="Collecting statistics"):
        try:
            # Load the data
            data = torch.load(pt_file)
            
            # Update statistics
            total_frames += 1
            total_nodes += data.num_nodes
            total_edges += data.num_edges
            
            # Update label counts
            for label in range(4):
                label_counts[label] += (data.y == label).sum().item()
        except Exception as e:
            print(f"Error processing {pt_file}: {e}")
    
    # Calculate average nodes and edges per frame
    avg_nodes_per_frame = total_nodes / total_frames if total_frames > 0 else 0
    avg_edges_per_frame = total_edges / total_frames if total_frames > 0 else 0
    
    # Calculate label percentages
    total_labels = sum(label_counts.values())
    label_percentages = {label: count / total_labels * 100 if total_labels > 0 else 0 for label, count in label_counts.items()}
    
    # Create summary
    summary = f"""
    GNN Frame Statistics
    -------------------
    Total frames: {total_frames}
    Total nodes: {total_nodes}
    Total edges: {total_edges}
    Average nodes per frame: {avg_nodes_per_frame:.2f}
    Average edges per frame: {avg_edges_per_frame:.2f}
    
    Label distribution:
    Unknown (0): {label_counts[0]} ({label_percentages[0]:.2f}%)
    Workstation (1): {label_counts[1]} ({label_percentages[1]:.2f}%)
    Robot (2): {label_counts[2]} ({label_percentages[2]:.2f}%)
    Boundary (3): {label_counts[3]} ({label_percentages[3]:.2f}%)
    """
    
    # Save summary
    with open(os.path.join(output_dir, "summary.txt"), "w") as f:
        f.write(summary)
    
    # Save statistics to CSV
    with open(os.path.join(output_dir, "frame_statistics.csv"), "w", newline="") as f:
        writer = csv.writer(f)
        writer.writerow(["Statistic", "Value"])
        writer.writerow(["Total frames", total_frames])
        writer.writerow(["Total nodes", total_nodes])
        writer.writerow(["Total edges", total_edges])
        writer.writerow(["Average nodes per frame", avg_nodes_per_frame])
        writer.writerow(["Average edges per frame", avg_edges_per_frame])
        writer.writerow(["Unknown (0) count", label_counts[0]])
        writer.writerow(["Unknown (0) percentage", label_percentages[0]])
        writer.writerow(["Workstation (1) count", label_counts[1]])
        writer.writerow(["Workstation (1) percentage", label_percentages[1]])
        writer.writerow(["Robot (2) count", label_counts[2]])
        writer.writerow(["Robot (2) percentage", label_percentages[2]])
        writer.writerow(["Boundary (3) count", label_counts[3]])
        writer.writerow(["Boundary (3) percentage", label_percentages[3]])
    
    print(summary)

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Convert annotated CSV files to GNN frames')
    parser.add_argument('--input_dir', type=str, 
                        default='/home/<USER>/MA-Yugi/data/05_annotated/boundary_only_improved_annotated_ws300_rob75_bound150_corner250',
                        help='Directory containing annotated CSV files')
    parser.add_argument('--output_dir', type=str, 
                        default='/home/<USER>/MA-Yugi/data/05_gnn_frames_direct',
                        help='Directory to save GNN frames')
    parser.add_argument('--voxel_size', type=float, default=0.1,
                        help='Size of voxels for discretizing the point cloud')
    parser.add_argument('--temporal_windows', type=int, nargs='+', default=[1, 3, 5],
                        help='List of temporal window sizes')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Find all CSV files
    csv_files = []
    for root, dirs, files in os.walk(args.input_dir):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
    
    # Process each CSV file
    for csv_file in csv_files:
        print(f"Processing {csv_file}...")
        process_csv_file(csv_file, args.output_dir, args.voxel_size, args.temporal_windows)
    
    # Collect statistics
    collect_statistics(args.output_dir)

if __name__ == "__main__":
    main()
