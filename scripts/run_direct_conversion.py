#!/usr/bin/env python3

import os
import argparse
import subprocess

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run direct conversion from annotated CSV to GNN frames')
    parser.add_argument('--input_dir', type=str, 
                        default='/home/<USER>/MA-Yugi/data/05_annotated/boundary_only_improved_annotated_ws300_rob75_bound150_corner250',
                        help='Directory containing annotated CSV files')
    parser.add_argument('--output_dir', type=str, 
                        default='/home/<USER>/MA-Yugi/data/05_gnn_frames_direct',
                        help='Directory to save GNN frames')
    parser.add_argument('--voxel_size', type=float, default=0.1,
                        help='Size of voxels for discretizing the point cloud')
    args = parser.parse_args()
    
    # Run the conversion script
    cmd = [
        'python3', 'direct_annotated_to_gnn.py',
        '--input_dir', args.input_dir,
        '--output_dir', args.output_dir,
        '--voxel_size', str(args.voxel_size)
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd)

if __name__ == "__main__":
    main()
