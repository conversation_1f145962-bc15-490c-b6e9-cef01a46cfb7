#!/usr/bin/env python3

import os
import argparse
import random
import shutil
import numpy as np
from tqdm import tqdm
import glob
import json

def count_frames(dataset_dir, temporal_window):
    """
    Count the number of frames in a dataset for a specific temporal window.

    Args:
        dataset_dir: Path to the dataset directory
        temporal_window: Temporal window size (1, 3, or 5)

    Returns:
        Number of frames
    """
    temporal_dir = os.path.join(dataset_dir, f"temporal_{temporal_window}")
    if not os.path.exists(temporal_dir):
        return 0

    pt_files = glob.glob(os.path.join(temporal_dir, "*.pt"))
    return len(pt_files)

def get_dataset_info(input_dir, temporal_windows=[1, 3, 5]):
    """
    Get information about all datasets in the input directory.

    Args:
        input_dir: Directory containing GNN frames
        temporal_windows: List of temporal window sizes

    Returns:
        List of dictionaries with dataset information
    """
    # Find all dataset directories
    dataset_dirs = []
    for item in os.listdir(input_dir):
        item_path = os.path.join(input_dir, item)
        if os.path.isdir(item_path) and not item.startswith('.'):
            # Check if it contains temporal_* directories
            has_temporal = False
            for temporal_window in temporal_windows:
                if os.path.exists(os.path.join(item_path, f"temporal_{temporal_window}")):
                    has_temporal = True
                    break

            if has_temporal:
                dataset_dirs.append(item_path)

    # Get information about each dataset
    datasets = []
    for dataset_dir in dataset_dirs:
        dataset_name = os.path.basename(dataset_dir)

        # Count frames for each temporal window
        frame_counts = {}
        for temporal_window in temporal_windows:
            frame_counts[temporal_window] = count_frames(dataset_dir, temporal_window)

        # Add dataset information
        datasets.append({
            'name': dataset_name,
            'path': dataset_dir,
            'frame_counts': frame_counts,
            'total_frames': sum(frame_counts.values())
        })

    return datasets

def split_datasets(datasets, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15, seed=42):
    """
    Split datasets into train, validation, and test sets.

    Args:
        datasets: List of dictionaries with dataset information
        train_ratio: Ratio of frames for training
        val_ratio: Ratio of frames for validation
        test_ratio: Ratio of frames for testing
        seed: Random seed

    Returns:
        Dictionary with train, val, and test datasets
    """
    # Set random seed for reproducibility
    random.seed(seed)

    # Sort datasets by total frames (descending)
    datasets = sorted(datasets, key=lambda x: x['total_frames'], reverse=True)

    # Calculate target frame counts
    total_frames = sum(dataset['total_frames'] for dataset in datasets)
    target_train_frames = int(total_frames * train_ratio)
    target_val_frames = int(total_frames * val_ratio)
    target_test_frames = total_frames - target_train_frames - target_val_frames

    # Initialize splits
    train_datasets = []
    val_datasets = []
    test_datasets = []

    train_frames = 0
    val_frames = 0
    test_frames = 0

    # Assign datasets to splits
    for dataset in datasets:
        # Calculate how far each split is from its target
        train_diff = target_train_frames - train_frames
        val_diff = target_val_frames - val_frames
        test_diff = target_test_frames - test_frames

        # Normalize differences to get probabilities
        total_diff = abs(train_diff) + abs(val_diff) + abs(test_diff)
        if total_diff == 0:
            # If all splits have reached their targets, distribute randomly
            probs = [1/3, 1/3, 1/3]
        else:
            # Higher probability for splits that are further from their targets
            probs = [abs(train_diff)/total_diff, abs(val_diff)/total_diff, abs(test_diff)/total_diff]

        # Adjust probabilities based on current frame counts
        if train_frames >= target_train_frames:
            probs[0] = 0
        if val_frames >= target_val_frames:
            probs[1] = 0
        if test_frames >= target_test_frames:
            probs[2] = 0

        # Normalize probabilities
        if sum(probs) > 0:
            probs = [p/sum(probs) for p in probs]
        else:
            # If all splits have reached their targets, distribute based on remaining capacity
            remaining = [target_train_frames - train_frames, target_val_frames - val_frames, target_test_frames - test_frames]
            total_remaining = sum(remaining)
            if total_remaining > 0:
                probs = [r/total_remaining for r in remaining]
            else:
                probs = [1/3, 1/3, 1/3]

        # Choose split based on probabilities
        split_idx = np.random.choice([0, 1, 2], p=probs)

        if split_idx == 0:
            train_datasets.append(dataset)
            train_frames += dataset['total_frames']
        elif split_idx == 1:
            val_datasets.append(dataset)
            val_frames += dataset['total_frames']
        else:
            test_datasets.append(dataset)
            test_frames += dataset['total_frames']

    # Calculate actual ratios
    actual_train_ratio = train_frames / total_frames
    actual_val_ratio = val_frames / total_frames
    actual_test_ratio = test_frames / total_frames

    print(f"Target ratios: Train={train_ratio:.2f}, Val={val_ratio:.2f}, Test={test_ratio:.2f}")
    print(f"Actual ratios: Train={actual_train_ratio:.2f}, Val={actual_val_ratio:.2f}, Test={actual_test_ratio:.2f}")
    print(f"Train: {train_frames} frames, {len(train_datasets)} datasets")
    print(f"Val: {val_frames} frames, {len(val_datasets)} datasets")
    print(f"Test: {test_frames} frames, {len(test_datasets)} datasets")

    return {
        'train': train_datasets,
        'val': val_datasets,
        'test': test_datasets,
        'stats': {
            'total_frames': total_frames,
            'train_frames': train_frames,
            'val_frames': val_frames,
            'test_frames': test_frames,
            'train_ratio': actual_train_ratio,
            'val_ratio': actual_val_ratio,
            'test_ratio': actual_test_ratio
        }
    }

def create_split_directories(output_dir, temporal_windows=[1, 3, 5]):
    """
    Create directories for train, validation, and test splits.

    Args:
        output_dir: Directory to save splits
        temporal_windows: List of temporal window sizes
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Create split directories
    for split in ['train', 'val', 'test']:
        split_dir = os.path.join(output_dir, split)
        os.makedirs(split_dir, exist_ok=True)

        # Create temporal directories
        for temporal_window in temporal_windows:
            temporal_dir = os.path.join(split_dir, f"temporal_{temporal_window}")
            os.makedirs(temporal_dir, exist_ok=True)

def copy_frames(datasets, split, input_dir, output_dir, temporal_windows=[1, 3, 5]):
    """
    Copy frames from datasets to the appropriate split directory.

    Args:
        datasets: List of dictionaries with dataset information
        split: Split name ('train', 'val', or 'test')
        input_dir: Directory containing GNN frames
        output_dir: Directory to save splits
        temporal_windows: List of temporal window sizes
    """
    # Create progress bar
    total_frames = sum(dataset['total_frames'] for dataset in datasets)
    pbar = tqdm(total=total_frames, desc=f"Copying {split} frames")

    # Copy frames for each dataset
    for dataset in datasets:
        dataset_name = dataset['name']
        dataset_dir = dataset['path']

        # Copy frames for each temporal window
        for temporal_window in temporal_windows:
            temporal_dir = os.path.join(dataset_dir, f"temporal_{temporal_window}")
            if not os.path.exists(temporal_dir):
                continue

            # Get all .pt files
            pt_files = glob.glob(os.path.join(temporal_dir, "*.pt"))

            # Copy each file
            for pt_file in pt_files:
                # Get file name
                file_name = os.path.basename(pt_file)

                # Create destination path
                dest_dir = os.path.join(output_dir, split, f"temporal_{temporal_window}")
                dest_path = os.path.join(dest_dir, file_name)

                # Copy file
                shutil.copy2(pt_file, dest_path)

                # Update progress bar
                pbar.update(1)

    # Close progress bar
    pbar.close()

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Split GNN frames into train, validation, and test sets')
    parser.add_argument('--input_dir', type=str, default='/home/<USER>/MA-Yugi/data/05_gnn_frames',
                        help='Directory containing GNN frames')
    parser.add_argument('--output_dir', type=str, default='/home/<USER>/MA-Yugi/data/06_gnn_ready',
                        help='Directory to save splits')
    parser.add_argument('--train_ratio', type=float, default=0.7,
                        help='Ratio of frames for training')
    parser.add_argument('--val_ratio', type=float, default=0.15,
                        help='Ratio of frames for validation')
    parser.add_argument('--test_ratio', type=float, default=0.15,
                        help='Ratio of frames for testing')
    parser.add_argument('--seed', type=int, default=42,
                        help='Random seed')
    parser.add_argument('--temporal_windows', type=int, nargs='+', default=[1, 3, 5],
                        help='List of temporal window sizes')
    parser.add_argument('--dry_run', action='store_true',
                        help='Dry run (do not copy files)')
    args = parser.parse_args()

    # Get dataset information
    print("Getting dataset information...")
    datasets = get_dataset_info(args.input_dir, args.temporal_windows)

    # Print dataset information
    print(f"Found {len(datasets)} datasets:")
    for dataset in datasets:
        print(f"  {dataset['name']}: {dataset['total_frames']} frames")
        for temporal_window in args.temporal_windows:
            print(f"    temporal_{temporal_window}: {dataset['frame_counts'][temporal_window]} frames")

    # Split datasets
    print("\nSplitting datasets...")
    splits = split_datasets(datasets, args.train_ratio, args.val_ratio, args.test_ratio, args.seed)

    # Print split information
    print("\nSplit information:")
    print("Train datasets:")
    for dataset in splits['train']:
        print(f"  {dataset['name']}: {dataset['total_frames']} frames")

    print("Validation datasets:")
    for dataset in splits['val']:
        print(f"  {dataset['name']}: {dataset['total_frames']} frames")

    print("Test datasets:")
    for dataset in splits['test']:
        print(f"  {dataset['name']}: {dataset['total_frames']} frames")

    # Save split information
    split_info = {
        'train': [dataset['name'] for dataset in splits['train']],
        'val': [dataset['name'] for dataset in splits['val']],
        'test': [dataset['name'] for dataset in splits['test']],
        'stats': splits['stats']
    }

    with open(os.path.join(args.output_dir, 'split_info.json'), 'w') as f:
        json.dump(split_info, f, indent=2)

    # Create split directories
    if not args.dry_run:
        print("\nCreating split directories...")
        create_split_directories(args.output_dir, args.temporal_windows)

        # Copy frames
        print("\nCopying frames...")
        copy_frames(splits['train'], 'train', args.input_dir, args.output_dir, args.temporal_windows)
        copy_frames(splits['val'], 'val', args.input_dir, args.output_dir, args.temporal_windows)
        copy_frames(splits['test'], 'test', args.input_dir, args.output_dir, args.temporal_windows)

        print("\nDone!")
    else:
        print("\nDry run completed. No files were copied.")

if __name__ == "__main__":
    main()
