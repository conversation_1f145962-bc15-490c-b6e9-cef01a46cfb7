#!/usr/bin/env python3

import os
import argparse
import glob
from PIL import Image
import imageio

def create_animation(frames_dir, output_file, fps=10):
    """
    Create an animation from frame images
    
    Args:
        frames_dir: Directory containing frame PNG images
        output_file: Path to save animation file (GIF or MP4)
        fps: Frames per second
    """
    # Find all frame images
    frame_files = sorted(glob.glob(os.path.join(frames_dir, "frame_*.png")))
    
    if not frame_files:
        print(f"No frame images found in {frames_dir}")
        return False
    
    print(f"Creating animation from {len(frame_files)} frames")
    
    # Determine output format from file extension
    output_ext = os.path.splitext(output_file)[1].lower()
    
    if output_ext == '.gif':
        # Create GIF animation
        images = [Image.open(f) for f in frame_files]
        images[0].save(
            output_file,
            save_all=True,
            append_images=images[1:],
            duration=1000//fps,  # Duration between frames in ms
            loop=0  # Loop forever
        )
    elif output_ext in ['.mp4', '.avi', '.mov']:
        # Create video animation
        writer = imageio.get_writer(output_file, fps=fps)
        
        for frame_file in frame_files:
            image = imageio.imread(frame_file)
            writer.append_data(image)
            
        writer.close()
    else:
        print(f"Unsupported output format: {output_ext}")
        return False
    
    print(f"Animation saved to: {output_file}")
    return True


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create animation from frame images")
    parser.add_argument('--input-dir', '-i', type=str, required=True,
                       help='Directory containing frame images')
    parser.add_argument('--output', '-o', type=str, required=True,
                       help='Output animation file path (GIF or MP4)')
    parser.add_argument('--fps', type=int, default=10,
                       help='Frames per second (default: 10)')
    
    args = parser.parse_args()
    
    create_animation(args.input_dir, args.output, args.fps)