#!/usr/bin/env python3

"""
Process All Scenarios

This script orchestrates the entire pipeline from extraction to GNN frame generation
for multiple scenarios.

Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
Date: 2025
"""

import os
import argparse
import logging
import subprocess
import glob
from datetime import datetime
import json
import yaml
import sys
from pathlib import Path


def setup_logging(log_dir=None, verbose=False):
    """Set up logging configuration"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"process_all_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        handlers = [
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    else:
        handlers = [logging.StreamHandler()]
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger("ProcessAll")


def load_config(config_file):
    """Load configuration from YAML file"""
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    
    return config


def run_command(command, logger):
    """Run a shell command and log the output"""
    logger.info(f"Running command: {command}")
    
    process = subprocess.Popen(
        command, shell=True,
        stdout=subprocess.PIPE, stderr=subprocess.PIPE,
        universal_newlines=True
    )
    
    stdout, stderr = process.communicate()
    
    if stdout:
        for line in stdout.splitlines():
            logger.info(line)
    
    if stderr:
        for line in stderr.splitlines():
            logger.error(line)
    
    return process.returncode


def process_scenario(scenario, config, logger):
    """Process a single scenario"""
    logger.info(f"Processing scenario: {scenario}")
    
    # Get scenario-specific configuration
    scenario_config_file = os.path.join(config['config_dir'], 'scenarios', f"{scenario.lower()}.yaml")
    if os.path.exists(scenario_config_file):
        scenario_config = load_config(scenario_config_file)
        logger.info(f"Loaded scenario configuration from {scenario_config_file}")
    else:
        scenario_config = {'scenario': scenario}
        logger.warning(f"No scenario configuration found for {scenario}")
    
    # Create output directories
    output_dir = os.path.join(config['output_dir'], scenario)
    os.makedirs(output_dir, exist_ok=True)
    
    # Define directories for each stage
    extracted_dir = os.path.join(output_dir, '01_extracted')
    synchronized_dir = os.path.join(output_dir, '02_synchronized')
    transformed_dir = os.path.join(output_dir, '03_transformed')
    cleaned_dir = os.path.join(output_dir, '04_cleaned')
    annotated_dir = os.path.join(output_dir, '05_annotated')
    gnn_frames_dir = os.path.join(output_dir, '06_gnn_frames')
    gnn_ready_dir = os.path.join(output_dir, '07_gnn_ready')
    
    # Create directories
    for directory in [extracted_dir, synchronized_dir, transformed_dir, cleaned_dir, annotated_dir, gnn_frames_dir, gnn_ready_dir]:
        os.makedirs(directory, exist_ok=True)
    
    # Step 1: Extract data
    if config['run_extraction']:
        logger.info("Step 1: Extracting data")
        
        # Extract radar data
        extract_radar_cmd = (
            f"python -m collaborative_perception.extraction.radar_extractor "
            f"--dataset-root {config['dataset_root']} "
            f"--output-root {extracted_dir}"
        )
        if run_command(extract_radar_cmd, logger) != 0:
            logger.error("Failed to extract radar data")
            return False
        
        # Extract Vicon data
        extract_vicon_cmd = (
            f"python -m collaborative_perception.extraction.vicon_extractor "
            f"--dataset-root {config['dataset_root']} "
            f"--output-root {extracted_dir}"
        )
        if run_command(extract_vicon_cmd, logger) != 0:
            logger.error("Failed to extract Vicon data")
            return False
    
    # Step 2: Synchronize data
    if config['run_synchronization']:
        logger.info("Step 2: Synchronizing data")
        
        # Find extracted data directories
        robot_dirs = glob.glob(os.path.join(extracted_dir, scenario, 'Robot_*'))
        
        for robot_dir in robot_dirs:
            robot_name = os.path.basename(robot_dir)
            logger.info(f"Synchronizing data for {robot_name}")
            
            # Find radar and Vicon data
            radar_dirs = glob.glob(os.path.join(robot_dir, '*'))
            
            for radar_dir in radar_dirs:
                # Synchronize data
                sync_cmd = (
                    f"python -m collaborative_perception.preprocessing.synchronizer "
                    f"--radar-dir {radar_dir} "
                    f"--vicon-dir {radar_dir} "
                    f"--output-dir {os.path.join(synchronized_dir, scenario, robot_name, os.path.basename(radar_dir))}"
                )
                if run_command(sync_cmd, logger) != 0:
                    logger.error(f"Failed to synchronize data for {radar_dir}")
                    return False
    
    # Step 3: Transform coordinates
    if config['run_transformation']:
        logger.info("Step 3: Transforming coordinates")
        
        # Find synchronized data files
        sync_files = glob.glob(os.path.join(synchronized_dir, scenario, '**', 'synchronized_data_*.csv'), recursive=True)
        
        for sync_file in sync_files:
            # Transform coordinates
            transform_cmd = (
                f"python -m collaborative_perception.transformation.transformer "
                f"--input {sync_file} "
                f"--output {os.path.join(transformed_dir, os.path.basename(sync_file).replace('synchronized', 'transformed'))} "
                f"--plot --save-plot {os.path.join(transformed_dir, os.path.basename(sync_file).replace('.csv', '_transform.png'))}"
            )
            if run_command(transform_cmd, logger) != 0:
                logger.error(f"Failed to transform coordinates for {sync_file}")
                return False
            
            # Evaluate transformation
            evaluate_cmd = (
                f"python -m collaborative_perception.transformation.evaluation.evaluator "
                f"--input {os.path.join(transformed_dir, os.path.basename(sync_file).replace('synchronized', 'transformed'))} "
                f"--output {os.path.join(transformed_dir, os.path.basename(sync_file).replace('synchronized.csv', 'evaluation.json'))} "
                f"--plot --plot-dir {transformed_dir}"
            )
            if run_command(evaluate_cmd, logger) != 0:
                logger.error(f"Failed to evaluate transformation for {sync_file}")
                return False
    
    # Step 4: Clean data
    if config['run_cleaning']:
        logger.info("Step 4: Cleaning data")
        
        # Find transformed data files
        transformed_files = glob.glob(os.path.join(transformed_dir, 'transformed_*.csv'))
        
        for transformed_file in transformed_files:
            # Clean data
            clean_cmd = (
                f"python -m collaborative_perception.cleaning.radar_cleaner "
                f"--input {transformed_file} "
                f"--output {os.path.join(cleaned_dir, os.path.basename(transformed_file).replace('transformed', 'cleaned'))} "
                f"--plot --save-plot {os.path.join(cleaned_dir, os.path.basename(transformed_file).replace('.csv', '_cleaning.png'))}"
            )
            if run_command(clean_cmd, logger) != 0:
                logger.error(f"Failed to clean data for {transformed_file}")
                return False
    
    # Step 5: Annotate point clouds
    if config['run_annotation']:
        logger.info("Step 5: Annotating point clouds")
        
        # Find cleaned data files
        cleaned_files = glob.glob(os.path.join(cleaned_dir, 'cleaned_*.csv'))
        
        for cleaned_file in cleaned_files:
            # Annotate point cloud
            annotate_cmd = (
                f"python -m collaborative_perception.annotation.annotator "
                f"--input {cleaned_file} "
                f"--output {os.path.join(annotated_dir, os.path.basename(cleaned_file).replace('cleaned', 'annotated'))} "
                f"--ws-position-file {config['ws_position_file']} "
                f"--plot --save-plot {os.path.join(annotated_dir, os.path.basename(cleaned_file).replace('.csv', '_annotation.png'))}"
            )
            if run_command(annotate_cmd, logger) != 0:
                logger.error(f"Failed to annotate point cloud for {cleaned_file}")
                return False
    
    # Step 6: Convert to GNN frames
    if config['run_conversion']:
        logger.info("Step 6: Converting to GNN frames")
        
        # Find annotated data files
        annotated_files = glob.glob(os.path.join(annotated_dir, 'annotated_*.csv'))
        
        for annotated_file in annotated_files:
            # Convert to GNN frames
            convert_cmd = (
                f"python -m collaborative_perception.conversion.gnn_converter "
                f"--input {annotated_file} "
                f"--output-dir {os.path.join(gnn_frames_dir, os.path.basename(annotated_file).replace('.csv', ''))} "
                f"--voxel-size {config.get('voxel_size', 0.1)} "
                f"--temporal-windows {' '.join(map(str, config.get('temporal_windows', [1, 3, 5])))}"
            )
            if run_command(convert_cmd, logger) != 0:
                logger.error(f"Failed to convert to GNN frames for {annotated_file}")
                return False
    
    # Step 7: Split dataset
    if config['run_splitting']:
        logger.info("Step 7: Splitting dataset")
        
        # Find GNN frame directories
        gnn_dirs = glob.glob(os.path.join(gnn_frames_dir, '*'))
        
        for gnn_dir in gnn_dirs:
            # Split dataset
            split_cmd = (
                f"python -m collaborative_perception.dataset.dataset_splitter "
                f"--input-dir {gnn_dir} "
                f"--output-dir {os.path.join(gnn_ready_dir, os.path.basename(gnn_dir))} "
                f"--train-ratio {config.get('train_ratio', 0.7)} "
                f"--val-ratio {config.get('val_ratio', 0.15)} "
                f"--test-ratio {config.get('test_ratio', 0.15)} "
                f"--seed {config.get('seed', 42)}"
            )
            if run_command(split_cmd, logger) != 0:
                logger.error(f"Failed to split dataset for {gnn_dir}")
                return False
    
    logger.info(f"Successfully processed scenario: {scenario}")
    return True


def main():
    """Main function to process all scenarios."""
    parser = argparse.ArgumentParser(description='Process all scenarios')
    parser.add_argument('--config', '-c', type=str, default='config/default_config.yaml',
                        help='Path to configuration file')
    parser.add_argument('--scenarios', '-s', type=str, nargs='+',
                        help='Scenarios to process (default: all scenarios in config)')
    parser.add_argument('--output-dir', '-o', type=str, default=None,
                        help='Output directory (default: from config)')
    parser.add_argument('--skip-extraction', action='store_true',
                        help='Skip data extraction step')
    parser.add_argument('--skip-synchronization', action='store_true',
                        help='Skip data synchronization step')
    parser.add_argument('--skip-transformation', action='store_true',
                        help='Skip coordinate transformation step')
    parser.add_argument('--skip-cleaning', action='store_true',
                        help='Skip data cleaning step')
    parser.add_argument('--skip-annotation', action='store_true',
                        help='Skip point cloud annotation step')
    parser.add_argument('--skip-conversion', action='store_true',
                        help='Skip GNN conversion step')
    parser.add_argument('--skip-splitting', action='store_true',
                        help='Skip dataset splitting step')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Load configuration
    try:
        config = load_config(args.config)
    except Exception as e:
        print(f"Error loading configuration: {e}")
        return 1
    
    # Override configuration with command-line arguments
    if args.output_dir:
        config['output_dir'] = args.output_dir
    
    # Set up logging
    log_dir = os.path.join(config['output_dir'], 'logs')
    logger = setup_logging(log_dir, args.verbose)
    
    # Set processing flags
    config['run_extraction'] = not args.skip_extraction
    config['run_synchronization'] = not args.skip_synchronization
    config['run_transformation'] = not args.skip_transformation
    config['run_cleaning'] = not args.skip_cleaning
    config['run_annotation'] = not args.skip_annotation
    config['run_conversion'] = not args.skip_conversion
    config['run_splitting'] = not args.skip_splitting
    
    # Get scenarios to process
    scenarios = args.scenarios or config.get('scenarios', [])
    
    if not scenarios:
        logger.error("No scenarios specified")
        return 1
    
    # Process each scenario
    success = True
    for scenario in scenarios:
        if not process_scenario(scenario, config, logger):
            logger.error(f"Failed to process scenario: {scenario}")
            success = False
    
    if success:
        logger.info("Successfully processed all scenarios")
        return 0
    else:
        logger.error("Failed to process some scenarios")
        return 1


if __name__ == '__main__':
    sys.exit(main())
