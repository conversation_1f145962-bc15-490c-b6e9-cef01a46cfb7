#!/usr/bin/env python3

"""
Data Extraction Script

This script provides a command-line interface for extracting radar and Vicon data
from ROS bags.

Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
Date: 2025
"""

import argparse
import os
import sys
import logging
from datetime import datetime


def setup_logging(log_dir=None, verbose=False):
    """Set up logging configuration"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"extraction_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        handlers = [
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    else:
        handlers = [logging.StreamHandler()]
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger("DataExtraction")


def extract_radar_data(args, logger):
    """Extract radar data from ROS bags"""
    logger.info("Extracting radar data...")
    
    try:
        from collaborative_perception.extraction.radar_extractor import process_rosbag, extract_all_radar_data
        
        if args.dataset_root:
            logger.info(f"Processing all rosbags in {args.dataset_root}")
            extract_all_radar_data(args.dataset_root, args.output_dir)
        elif args.rosbag_path:
            logger.info(f"Processing rosbag at {args.rosbag_path}")
            process_rosbag(args.rosbag_path, args.robot_id, args.output_dir, args.scenario)
        else:
            logger.error("Either --dataset-root or --rosbag-path must be specified")
            return 1
            
        logger.info("Radar data extraction completed successfully")
        return 0
        
    except ImportError as e:
        logger.error(f"Error importing radar extractor module: {e}")
        logger.error("Make sure the collaborative_perception package is installed")
        return 1
    except Exception as e:
        logger.error(f"Error extracting radar data: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


def extract_vicon_data(args, logger):
    """Extract Vicon data from ROS bags"""
    logger.info("Extracting Vicon data...")
    
    try:
        from collaborative_perception.extraction.vicon_extractor import process_rosbag, extract_all_vicon_data
        
        if args.dataset_root:
            logger.info(f"Processing all rosbags in {args.dataset_root}")
            extract_all_vicon_data(args.dataset_root, args.output_dir)
        elif args.rosbag_path:
            logger.info(f"Processing rosbag at {args.rosbag_path}")
            process_rosbag(args.rosbag_path, args.output_dir, args.scenario)
        else:
            logger.error("Either --dataset-root or --rosbag-path must be specified")
            return 1
            
        logger.info("Vicon data extraction completed successfully")
        return 0
        
    except ImportError as e:
        logger.error(f"Error importing Vicon extractor module: {e}")
        logger.error("Make sure the collaborative_perception package is installed")
        return 1
    except Exception as e:
        logger.error(f"Error extracting Vicon data: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


def extract_all_data(args, logger):
    """Extract both radar and Vicon data"""
    logger.info("Extracting both radar and Vicon data...")
    
    radar_result = extract_radar_data(args, logger)
    vicon_result = extract_vicon_data(args, logger)
    
    if radar_result == 0 and vicon_result == 0:
        logger.info("All data extraction completed successfully")
        return 0
    else:
        logger.error("Data extraction failed")
        return 1


def main():
    """Main function to run the data extraction script."""
    parser = argparse.ArgumentParser(description='Extract data from ROS bags')
    parser.add_argument('--data-type', '-t', type=str, choices=['radar', 'vicon', 'all'], default='all',
                        help='Type of data to extract (default: all)')
    parser.add_argument('--rosbag-path', '-r', type=str, required=False,
                        help='Path to the rosbag directory')
    parser.add_argument('--robot-id', '-i', type=str, default='ep03',
                        help='ID of the robot for radar extraction (default: ep03)')
    parser.add_argument('--output-dir', '-o', type=str, default=None,
                        help='Directory to save extracted data')
    parser.add_argument('--scenario', '-s', type=str, default='unknown_scenario',
                        help='Name of the scenario (default: unknown_scenario)')
    parser.add_argument('--dataset-root', '-d', type=str, default=None,
                        help='Root directory of the dataset for batch processing')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Set default output directory if not specified
    if not args.output_dir:
        if args.dataset_root:
            args.output_dir = os.path.join(os.path.dirname(args.dataset_root), 'Extracted_Data')
        elif args.rosbag_path:
            args.output_dir = os.path.join(os.path.dirname(args.rosbag_path), 'Extracted_Data')
        else:
            args.output_dir = 'Extracted_Data'
    
    # Set up logging
    logger = setup_logging(
        log_dir=os.path.join(args.output_dir, 'logs'),
        verbose=args.verbose
    )
    
    # Extract data based on the specified type
    if args.data_type == 'radar':
        return extract_radar_data(args, logger)
    elif args.data_type == 'vicon':
        return extract_vicon_data(args, logger)
    else:  # 'all'
        return extract_all_data(args, logger)


if __name__ == "__main__":
    sys.exit(main())
