#!/usr/bin/env python3

"""
Main entry point for the Collaborative Perception Pipeline.

This script provides a command-line interface to run the complete
collaborative perception pipeline or specific components.

Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
Date: 2025
"""

import argparse
import os
import sys
import logging
from datetime import datetime
import yaml


def setup_logging(log_dir=None, verbose=False):
    """Set up logging configuration"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        handlers = [
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    else:
        handlers = [logging.StreamHandler()]
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger("CollaborativePerception")


def load_config(config_file):
    """Load configuration from YAML file"""
    if not os.path.exists(config_file):
        raise FileNotFoundError(f"Configuration file not found: {config_file}")
    
    with open(config_file, 'r') as f:
        config = yaml.safe_load(f)
    
    return config


def run_pipeline(args):
    """Run the complete pipeline"""
    logger = setup_logging(
        log_dir=os.path.join(args.output_dir, 'logs') if args.output_dir else None,
        verbose=args.verbose
    )
    
    logger.info("Starting Collaborative Perception Pipeline")
    
    # Load configuration if provided
    config = None
    if args.config_file:
        try:
            config = load_config(args.config_file)
            logger.info(f"Loaded configuration from {args.config_file}")
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return 1
    
    # Import the pipeline module
    try:
        from collaborative_perception.pipeline.pipeline import CollaborativePerceptionPipeline
        
        # Create configuration from command-line arguments if not loaded from file
        if not config:
            config = {
                'input_dir': args.input_dir,
                'output_dir': args.output_dir,
                'scenario': args.scenario,
                'robot1_dir': args.robot1_dir,
                'robot2_dir': args.robot2_dir,
                'vicon_file': args.vicon_file,
                'generate_plots': not args.no_plots,
                'save_plots': not args.no_save_plots,
                'verbose': args.verbose
            }
        
        # Create and run pipeline
        pipeline = CollaborativePerceptionPipeline(config)
        results = pipeline.run_pipeline()
        
        # Save summary
        summary_file = pipeline.save_summary(results)
        
        if results['status'] == 'success':
            logger.info("Pipeline completed successfully")
            logger.info(f"Results summary saved to: {summary_file}")
            return 0
        else:
            logger.error(f"Pipeline failed: {results['message']}")
            return 1
        
    except ImportError as e:
        logger.error(f"Error importing pipeline module: {e}")
        logger.error("Make sure the collaborative_perception package is installed")
        return 1
    except Exception as e:
        logger.error(f"Error running pipeline: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


def run_component(args):
    """Run a specific pipeline component"""
    logger = setup_logging(
        log_dir=os.path.join(args.output_dir, 'logs') if args.output_dir else None,
        verbose=args.verbose
    )
    
    logger.info(f"Running component: {args.component}")
    
    # Map component names to module paths
    component_modules = {
        'extraction': 'collaborative_perception.extraction',
        'synchronization': 'collaborative_perception.preprocessing.synchronizer',
        'transformation': 'collaborative_perception.transformation.transformer',
        'cleaning': 'collaborative_perception.cleaning.radar_cleaner',
        'annotation': 'collaborative_perception.annotation.annotator',
        'conversion': 'collaborative_perception.conversion.gnn_converter',
        'splitting': 'collaborative_perception.dataset.dataset_splitter',
        'visualization': 'collaborative_perception.visualization.global_map'
    }
    
    if args.component not in component_modules:
        logger.error(f"Unknown component: {args.component}")
        logger.error(f"Available components: {', '.join(component_modules.keys())}")
        return 1
    
    # Import the component module
    try:
        module_path = component_modules[args.component]
        module_name = module_path.split('.')[-1]
        
        # Import the module
        from importlib import import_module
        module = import_module(module_path)
        
        # Get the main function
        if hasattr(module, 'main'):
            # Pass remaining arguments to the component's main function
            sys.argv = [sys.argv[0]] + args.component_args
            return module.main()
        else:
            logger.error(f"Component {args.component} does not have a main function")
            return 1
        
    except ImportError as e:
        logger.error(f"Error importing component module: {e}")
        logger.error("Make sure the collaborative_perception package is installed")
        return 1
    except Exception as e:
        logger.error(f"Error running component: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 1


def main():
    """Main entry point"""
    # Create the top-level parser
    parser = argparse.ArgumentParser(
        description='Collaborative Perception Framework',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run the complete pipeline
  python run_pipeline.py pipeline --input-dir /path/to/data --scenario CPPS_Horizontal
  
  # Run a specific component
  python run_pipeline.py component transformation --input /path/to/data.csv --output /path/to/output.csv
  
  # Run with configuration file
  python run_pipeline.py pipeline --config-file /path/to/config.yaml
"""
    )
    parser.add_argument('--version', action='version', version='Collaborative Perception Framework v0.1.0')
    
    # Create subparsers for different commands
    subparsers = parser.add_subparsers(dest='command', help='Command to run')
    
    # Parser for the 'pipeline' command
    pipeline_parser = subparsers.add_parser('pipeline', help='Run the complete pipeline')
    pipeline_parser.add_argument('--input-dir', '-i', type=str, required=False,
                        help='Path to the synchronized data directory')
    pipeline_parser.add_argument('--output-dir', '-o', type=str, default='output',
                        help='Path to store output files (default: output)')
    pipeline_parser.add_argument('--scenario', '-s', type=str, default='CPPS_Horizontal',
                        help='Scenario to process (default: CPPS_Horizontal)')
    pipeline_parser.add_argument('--robot1-dir', type=str, default=None,
                        help='Directory containing Robot 1 data (default: auto-detect)')
    pipeline_parser.add_argument('--robot2-dir', type=str, default=None,
                        help='Directory containing Robot 2 data (default: auto-detect)')
    pipeline_parser.add_argument('--vicon-file', type=str, default=None,
                        help='Vicon data file (default: auto-detect)')
    pipeline_parser.add_argument('--config-file', '-c', type=str, default=None,
                        help='Path to configuration file (YAML format)')
    pipeline_parser.add_argument('--no-plots', action='store_true',
                        help='Disable plot generation')
    pipeline_parser.add_argument('--no-save-plots', action='store_true',
                        help='Do not save generated plots')
    pipeline_parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose output')
    
    # Parser for the 'component' command
    component_parser = subparsers.add_parser('component', help='Run a specific pipeline component')
    component_parser.add_argument('component', type=str,
                        help='Component to run (extraction, synchronization, transformation, cleaning, annotation, conversion, splitting, visualization)')
    component_parser.add_argument('--output-dir', '-o', type=str, default='output',
                        help='Path to store output files (default: output)')
    component_parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose output')
    component_parser.add_argument('component_args', nargs='*',
                        help='Arguments to pass to the component')
    
    # Parse arguments
    args = parser.parse_args()
    
    # Run the appropriate command
    if args.command == 'pipeline':
        return run_pipeline(args)
    elif args.command == 'component':
        return run_component(args)
    else:
        parser.print_help()
        return 0


if __name__ == "__main__":
    sys.exit(main())
