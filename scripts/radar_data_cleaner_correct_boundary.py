#!/usr/bin/env python3

import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import glob

class RoboFUSEDataCleaner:
    """
    Cleans transformed point cloud data from RoboFUSE dataset for GNN training
    """

    def __init__(self, input_path, output_path=None):
        self.input_path = input_path
        self.output_path = output_path

        # Cleaning parameters - adjust based on your specific requirements
        self.params = {
            'boundary': {
                'x_min': -10.7, 'x_max': 10.9,
                'y_min': -5.5, 'y_max': 6.5,
                'z_min': 0.03, 'z_max': 3.0
            },
            'snr': {
                'min_value': 5.0  # Minimum SNR for reliable detection
            },
            'height': {
                'min_height': 0.05,  # Slightly above ground to filter ground plane
                'max_height': 2.5    # Maximum reasonable height in warehouse
            },
            'fov': {
                'angle_range': 360.0  # Field of view in degrees - increased from 180.0 to be less restrictive
            },
            'statistical_filter': {
                'k_neighbors': 10,    # Number of neighbors for density estimation
                'std_deviation': 3.0  # Standard deviation threshold (higher = more points kept)
            }
        }

        self.data = None
        self.cleaned_data = None

    def load_data(self):
        """Load transformed data from CSV"""
        try:
            print(f"Loading transformed data from: {self.input_path}")

            # Check file format based on content (tab or comma separated)
            with open(self.input_path, 'r') as f:
                first_line = f.readline()

            # Determine delimiter
            delimiter = '\t' if '\t' in first_line else ','

            self.data = pd.read_csv(self.input_path, delimiter=delimiter)
            print(f"Loaded {len(self.data)} data points with {len(self.data.columns)} columns")
            return True
        except Exception as e:
            print(f"Error loading data: {e}")
            return False

    def clean_data(self):
        """Apply cleaning operations to the data"""
        if self.data is None:
            print("No data loaded")
            return None

        print("Starting data cleaning...")
        cleaned = self.data.copy()
        original_count = len(cleaned)

        # 1. Apply boundary filters for both robots
        print("Applying boundary filters...")
        for robot_id in [1, 2]:
            # X boundary
            x_column = f'robot_{robot_id}_global_x_radar'
            if x_column in cleaned.columns:
                mask = (cleaned[x_column] >= self.params['boundary']['x_min']) & \
                       (cleaned[x_column] <= self.params['boundary']['x_max'])
                cleaned = cleaned[mask]
                print(f"  After {x_column} boundary filter: {len(cleaned)} points")

            # Y boundary
            y_column = f'robot_{robot_id}_global_y_radar'
            if y_column in cleaned.columns:
                mask = (cleaned[y_column] >= self.params['boundary']['y_min']) & \
                       (cleaned[y_column] <= self.params['boundary']['y_max'])
                cleaned = cleaned[mask]
                print(f"  After {y_column} boundary filter: {len(cleaned)} points")

            # Z boundary
            z_column = f'robot_{robot_id}_global_z_radar'
            if z_column in cleaned.columns:
                mask = (cleaned[z_column] >= self.params['boundary']['z_min']) & \
                       (cleaned[z_column] <= self.params['boundary']['z_max'])
                cleaned = cleaned[mask]
                print(f"  After {z_column} boundary filter: {len(cleaned)} points")

        # 2. Apply SNR filters
        print("Applying SNR filters...")
        for robot_id in [1, 2]:
            snr_column = f'robot_{robot_id}_global_snr_radar'
            if snr_column in cleaned.columns:
                mask = cleaned[snr_column] >= self.params['snr']['min_value']
                cleaned = cleaned[mask]
                print(f"  After {snr_column} filter: {len(cleaned)} points")

        # 3. Apply height filters
        print("Applying height filters...")
        for robot_id in [1, 2]:
            z_column = f'robot_{robot_id}_global_z_radar'
            if z_column in cleaned.columns:
                mask = (cleaned[z_column] >= self.params['height']['min_height']) & \
                       (cleaned[z_column] <= self.params['height']['max_height'])
                cleaned = cleaned[mask]
                print(f"  After {z_column} height filter: {len(cleaned)} points")

        # 4. Apply FOV filters
        print("Applying FOV filters...")
        # Store points before FOV filtering
        before_fov_count = len(cleaned)
        print(f"  Before FOV filtering: {before_fov_count} points")

        for robot_id in [1, 2]:
            # Get azimuth column and check if we have the robot's orientation
            azimuth_column = f'robot_{robot_id}_global_azimuth_radar'
            robot_yaw_column = f'robot_{robot_id}_yaw'

            if azimuth_column in cleaned.columns and robot_yaw_column in cleaned.columns:
                # Calculate absolute angle difference (handling circular angles)
                cleaned['angle_diff'] = np.abs(
                    np.arctan2(
                        np.sin(cleaned[azimuth_column] - cleaned[robot_yaw_column]),
                        np.cos(cleaned[azimuth_column] - cleaned[robot_yaw_column])
                    )
                )

                # Convert FOV range to radians
                half_fov = np.radians(self.params['fov']['angle_range'] / 2)

                # Filter points outside the FOV
                mask = cleaned['angle_diff'] <= half_fov
                cleaned = cleaned[mask]
                print(f"  After {azimuth_column} FOV filter: {len(cleaned)} points")

                # Drop temporary column
                cleaned = cleaned.drop('angle_diff', axis=1)

        # 5. Statistical outlier removal - DISABLED
        print("Statistical outlier removal is disabled")

        # Store cleaned data
        self.cleaned_data = cleaned

        # Report cleaning results
        removed_count = original_count - len(cleaned)
        removal_percentage = (removed_count / original_count) * 100 if original_count > 0 else 0
        print(f"Cleaning complete: Removed {removed_count} points ({removal_percentage:.2f}%)")

        return cleaned

    def generate_visualization(self):
        """Generate comparison visualization between original and cleaned data"""
        if self.data is None or self.cleaned_data is None:
            print("Both original and cleaned data must be available for visualization")
            return None

        # Create a figure with two columns for comparison
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Top view comparison (X-Y plane)
        self._plot_top_view(axes[0, 0], self.data, "Original Data (Top View)")
        self._plot_top_view(axes[0, 1], self.cleaned_data, "Cleaned Data (Top View)")

        # Side view comparison (X-Z plane)
        self._plot_side_view(axes[1, 0], self.data, "Original Data (Side View)")
        self._plot_side_view(axes[1, 1], self.cleaned_data, "Cleaned Data (Side View)")

        # Set overall title
        plt.suptitle(f"Data Cleaning Results for {os.path.basename(self.input_path)}", fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.96])

        return fig

    def _plot_top_view(self, ax, data, title):
        """Helper to plot top view (X-Y plane)"""
        # Plot data for each robot with different colors
        for robot_id in [1, 2]:
            x_col = f'robot_{robot_id}_global_x_radar'
            y_col = f'robot_{robot_id}_global_y_radar'

            if x_col in data.columns and y_col in data.columns:
                ax.scatter(
                    data[x_col],
                    data[y_col],
                    alpha=0.5,
                    s=1.5,
                    label=f'Robot {robot_id}'
                )

        # Draw warehouse boundaries
        boundary_x = [
            self.params['boundary']['x_min'], self.params['boundary']['x_max'],
            self.params['boundary']['x_max'], self.params['boundary']['x_min'],
            self.params['boundary']['x_min']
        ]
        boundary_y = [
            self.params['boundary']['y_min'], self.params['boundary']['y_min'],
            self.params['boundary']['y_max'], self.params['boundary']['y_max'],
            self.params['boundary']['y_min']
        ]
        ax.plot(boundary_x, boundary_y, 'k-', linewidth=2, label='Boundary')

        # Plot robot positions if available
        for robot_id in [1, 2]:
            pos_x_col = f'robot_{robot_id}_global_x'
            pos_y_col = f'robot_{robot_id}_global_y'

            if pos_x_col in data.columns and pos_y_col in data.columns:
                ax.scatter(
                    data[pos_x_col].iloc[0],
                    data[pos_y_col].iloc[0],
                    color='black',
                    marker='^',
                    s=100,
                    label=f'Robot {robot_id} Position' if robot_id == 1 else ""
                )

        ax.set_title(f'{title} ({len(data)} points)')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right')
        ax.set_aspect('equal')

    def _plot_side_view(self, ax, data, title):
        """Helper to plot side view (X-Z plane)"""
        # Plot data for each robot with different colors
        for robot_id in [1, 2]:
            x_col = f'robot_{robot_id}_global_x_radar'
            z_col = f'robot_{robot_id}_global_z_radar'

            if x_col in data.columns and z_col in data.columns:
                ax.scatter(
                    data[x_col],
                    data[z_col],
                    alpha=0.5,
                    s=1.5,
                    label=f'Robot {robot_id}'
                )

        # Draw height boundaries
        ax.axhline(self.params['height']['min_height'], color='r', linestyle='--', label='Min Height')
        ax.axhline(self.params['height']['max_height'], color='r', linestyle='--', label='Max Height')

        # Draw X boundaries
        ax.axvline(self.params['boundary']['x_min'], color='k', linestyle='--')
        ax.axvline(self.params['boundary']['x_max'], color='k', linestyle='--')

        ax.set_title(title)
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Z (m)')
        ax.grid(True, alpha=0.3)
        ax.legend(loc='upper right')

    def save_results(self):
        """Save cleaned data and visualization"""
        if self.cleaned_data is None:
            print("No cleaned data to save")
            return None, None

        # Determine output path for cleaned data
        if self.output_path is None:
            base_path = Path(self.input_path)
            self.output_path = base_path.parent / f"cleaned_{base_path.name}"

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.output_path), exist_ok=True)

        # Save cleaned data
        self.cleaned_data.to_csv(self.output_path, index=False)
        print(f"Saved cleaned data to: {self.output_path}")

        # Generate and save visualization
        fig = self.generate_visualization()
        if fig:
            viz_path = os.path.join(
                os.path.dirname(self.output_path),
                f"cleaning_comparison_{os.path.basename(self.output_path)}.png"
            )
            fig.savefig(viz_path, dpi=300, bbox_inches='tight')
            plt.close(fig)
            print(f"Saved visualization to: {viz_path}")
            return self.output_path, viz_path

        return self.output_path, None

    def process(self):
        """Execute the complete cleaning pipeline"""
        if not self.load_data():
            return None, None

        self.clean_data()
        return self.save_results()


def process_directory(input_dir, output_dir=None, pattern="transformed_*.csv"):
    """Process all transformed files in a directory structure recursively"""
    # Find all transformed files
    files = []
    for path in Path(input_dir).rglob(pattern):
        files.append(str(path))

    print(f"Found {len(files)} files to process")

    for i, file_path in enumerate(files):
        print(f"\n[{i+1}/{len(files)}] Processing file: {file_path}")

        # Create parallel output directory structure
        if output_dir:
            rel_path = os.path.relpath(os.path.dirname(file_path), input_dir)
            out_dir = os.path.join(output_dir, rel_path)
            os.makedirs(out_dir, exist_ok=True)
            output_path = os.path.join(out_dir, f"cleaned_{os.path.basename(file_path)}")
        else:
            output_path = None

        # Process file
        cleaner = RoboFUSEDataCleaner(file_path, output_path)
        cleaner.process()


def main():
    parser = argparse.ArgumentParser(description="Clean RoboFUSE transformed data for GNN training")
    parser.add_argument("--input", "-i", type=str,
                       default="/media/yugi/MAIN DRIVE/RoboFUSE_Dataset/Transformed_Data",
                       help="Input CSV file or directory")
    parser.add_argument("--output", "-o", type=str,
                       default="data/Cleaned_data_variations/correct_boundary_fov120",
                       help="Output path for cleaned data")
    parser.add_argument("--single-file", "-s", action="store_true",
                       help="Process only a single file (default: process directory)")
    parser.add_argument("--snr", type=float, default=10.0,
                       help="Minimum SNR threshold (default: 10.0)")
    parser.add_argument("--min-height", type=float, default=0.05,
                       help="Minimum height in meters (default: 0.05)")
    parser.add_argument("--max-height", type=float, default=2.5,
                       help="Maximum height in meters (default: 2.5)")
    parser.add_argument("--fov", type=float, default=120.0,
                       help="Robot field of view in degrees (default: 120.0)")

    args = parser.parse_args()

    if args.single_file:
        cleaner = RoboFUSEDataCleaner(args.input, args.output)

        # Update parameters if provided
        cleaner.params['snr']['min_value'] = args.snr
        cleaner.params['height']['min_height'] = args.min_height
        cleaner.params['height']['max_height'] = args.max_height
        cleaner.params['fov']['angle_range'] = args.fov

        cleaner.process()
    else:
        # Process all files in directory
        process_directory(args.input, args.output)

    return 0


if __name__ == "__main__":
    main()