#!/usr/bin/env python3

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import (
    GraphSAGE,
    GATv2Conv,
    EdgeConv,
    global_mean_pool,
    global_max_pool,
    BatchNorm
)
from torch_geometric.nn.conv import MessagePassing
from torch_geometric.nn.dense.linear import Linear
from torch_geometric.typing import Adj, OptTensor, PairTensor
from typing import Dict, List, Optional, Tuple, Union, Callable
import yaml


class ECCConv(MessagePassing):
    """
    Edge-Conditioned Convolution layer.

    This layer applies different weights to different edges based on edge features.
    """

    def __init__(
        self,
        in_channels: int,
        out_channels: int,
        edge_dim: int,
        aggr: str = 'mean',
    ):
        """
        Initialize the ECC layer.

        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            edge_dim: Dimension of edge features
            aggr: Aggregation method ('add', 'mean', or 'max')
        """
        super().__init__(aggr=aggr)

        self.in_channels = in_channels
        self.out_channels = out_channels

        # Network to generate weights from edge features
        self.edge_nn = nn.Sequential(
            nn.Linear(edge_dim, in_channels * out_channels),
            nn.ReLU(),
            nn.Linear(in_channels * out_channels, in_channels * out_channels)
        )

        # Linear transformation for self-loops
        self.root = Linear(in_channels, out_channels)

        self.reset_parameters()

    def reset_parameters(self):
        """Reset parameters."""
        for layer in self.edge_nn:
            if hasattr(layer, 'reset_parameters'):
                layer.reset_parameters()
        self.root.reset_parameters()

    def forward(
        self,
        x: Union[torch.Tensor, PairTensor],
        edge_index: Adj,
        edge_attr: OptTensor = None,
    ) -> torch.Tensor:
        """
        Forward pass.

        Args:
            x: Node features
            edge_index: Edge indices
            edge_attr: Edge features

        Returns:
            Updated node features
        """
        # Generate spatial edge features if not provided
        if edge_attr is None:
            # Get source and target node positions
            row, col = edge_index
            edge_attr = torch.norm(x[row, 3:6] - x[col, 3:6], dim=1, keepdim=True)

        # Propagate messages
        out = self.propagate(edge_index, x=x, edge_attr=edge_attr)

        # Apply self-loops
        out += self.root(x)

        return out

    def message(
        self,
        x_j: torch.Tensor,
        edge_attr: torch.Tensor,
    ) -> torch.Tensor:
        """
        Message function.

        Args:
            x_j: Source node features
            edge_attr: Edge features

        Returns:
            Messages
        """
        # Generate weights from edge features
        weight = self.edge_nn(edge_attr)
        weight = weight.view(-1, self.in_channels, self.out_channels)

        # Apply weights to source node features
        return torch.matmul(x_j.unsqueeze(1), weight).squeeze(1)


class OccupancyGNN(nn.Module):
    """
    GNN model for occupancy prediction.

    This model uses different GNN layers (GraphSAGE, GATv2, or ECC) to predict
    whether points in a robotic environment are occupied or unoccupied.
    """

    def __init__(
        self,
        input_dim: int,
        hidden_dim: int,
        output_dim: int = 1,
        num_layers: int = 3,
        dropout: float = 0.2,
        gnn_type: str = "graphsage",
        skip_connections: bool = True,
        batch_norm: bool = True,
        layer_norm: bool = False,
        pooling: str = "mean_max",
        attention_heads: int = 4,
    ):
        """
        Initialize the model.

        Args:
            input_dim: Dimension of input features
            hidden_dim: Dimension of hidden features
            output_dim: Dimension of output features
            num_layers: Number of GNN layers
            dropout: Dropout rate
            gnn_type: Type of GNN layer ('graphsage', 'gatv2', or 'ecc')
            skip_connections: Whether to use skip connections
            batch_norm: Whether to use batch normalization
            layer_norm: Whether to use layer normalization
            pooling: Pooling method ('mean', 'max', or 'mean_max')
            attention_heads: Number of attention heads for GATv2
        """
        super().__init__()

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.output_dim = output_dim
        self.num_layers = num_layers
        self.dropout = dropout
        self.gnn_type = gnn_type
        self.skip_connections = skip_connections
        self.batch_norm = batch_norm
        self.layer_norm = layer_norm
        self.pooling = pooling
        self.attention_heads = attention_heads

        # Initial feature embedding
        self.embedding = nn.Linear(input_dim, hidden_dim)

        # GNN layers
        self.convs = nn.ModuleList()

        # Batch normalization layers
        self.batch_norms = nn.ModuleList() if batch_norm else None

        # Layer normalization layers
        self.layer_norms = nn.ModuleList() if layer_norm else None

        # Create GNN layers
        for i in range(num_layers):
            in_channels = hidden_dim
            out_channels = hidden_dim

            if gnn_type == "graphsage":
                self.convs.append(
                    GraphSAGE(
                        in_channels=in_channels,
                        hidden_channels=hidden_dim,
                        num_layers=1,
                        out_channels=out_channels,
                        dropout=dropout,
                    )
                )
            elif gnn_type == "gatv2":
                self.convs.append(
                    GATv2Conv(
                        in_channels=in_channels,
                        out_channels=out_channels // attention_heads,
                        heads=attention_heads,
                        dropout=dropout,
                        concat=True,
                    )
                )
            elif gnn_type == "ecc":
                self.convs.append(
                    ECCConv(
                        in_channels=in_channels,
                        out_channels=out_channels,
                        edge_dim=1,  # Spatial distance as edge feature
                    )
                )
            else:
                raise ValueError(f"Unknown GNN type: {gnn_type}")

            # Add batch normalization layer
            if batch_norm:
                self.batch_norms.append(BatchNorm(hidden_dim))

            # Add layer normalization layer
            if layer_norm:
                self.layer_norms.append(nn.LayerNorm(hidden_dim))

        # MLP classifier
        self.mlp = nn.Sequential(
            nn.Linear(hidden_dim * (2 if pooling == "mean_max" else 1), hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, output_dim),
        )

    def forward(
        self,
        x: torch.Tensor,
        edge_index: torch.Tensor,
        batch: torch.Tensor,
    ) -> torch.Tensor:
        """
        Forward pass.

        Args:
            x: Node features
            edge_index: Edge indices
            batch: Batch indices

        Returns:
            Occupancy predictions
        """
        # Initial feature embedding
        h = self.embedding(x)

        # Apply GNN layers with skip connections
        for i in range(self.num_layers):
            # Apply GNN layer
            if self.gnn_type == "ecc":
                # Generate spatial edge features (only X and Y)
                row, col = edge_index
                edge_attr = torch.norm(x[row, 2:4] - x[col, 2:4], dim=1, keepdim=True)  # Only X and Y (indices 2-3)
                h_conv = self.convs[i](h, edge_index, edge_attr)
            else:
                h_conv = self.convs[i](h, edge_index)

            # Apply skip connection
            if self.skip_connections and i > 0:
                h = h_conv + h
            else:
                h = h_conv

            # Apply batch normalization
            if self.batch_norm:
                h = self.batch_norms[i](h)

            # Apply layer normalization
            if self.layer_norm:
                h = self.layer_norms[i](h)

            # Apply activation and dropout
            h = F.relu(h)
            h = F.dropout(h, p=self.dropout, training=self.training)

        # Global pooling
        if self.pooling == "mean":
            h_graph = global_mean_pool(h, batch)
        elif self.pooling == "max":
            h_graph = global_max_pool(h, batch)
        elif self.pooling == "mean_max":
            h_mean = global_mean_pool(h, batch)
            h_max = global_max_pool(h, batch)
            h_graph = torch.cat([h_mean, h_max], dim=1)
        else:
            raise ValueError(f"Unknown pooling method: {self.pooling}")

        # Apply MLP classifier
        out = self.mlp(h_graph)

        return out


def create_model(config: Dict) -> OccupancyGNN:
    """
    Create a model from configuration.

    Args:
        config: Configuration dictionary

    Returns:
        Model
    """
    # Determine input dimension based on temporal window
    input_dim = config["model"]["input_dim"]

    # Create model
    model = OccupancyGNN(
        input_dim=input_dim,
        hidden_dim=config["model"]["hidden_dim"],
        output_dim=config["model"]["output_dim"],
        num_layers=config["model"]["num_layers"],
        dropout=config["model"]["dropout"],
        gnn_type=config["model"]["gnn_type"],
        skip_connections=config["model"]["skip_connections"],
        batch_norm=config["model"]["batch_norm"],
        layer_norm=config["model"].get("layer_norm", False),
        pooling=config["model"]["pooling"],
        attention_heads=config["model"].get("attention_heads", 4),
    )

    return model


if __name__ == "__main__":
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)

    # Create model
    model = create_model(config)

    # Print model summary
    print(model)
