#!/usr/bin/env python3

import os
import argparse
import yaml
import torch
from typing import Dict, List, Tuple, Optional, Union, Any

from data import create_data_loaders
from model import create_model
from train import Trainer
from evaluate import Evaluator
from ablation import AblationStudy
from utils import set_seed, setup_logging, load_config, save_config, count_parameters


def parse_args():
    """
    Parse command line arguments.

    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(description="GNN-based Occupancy Prediction System")

    parser.add_argument(
        "--config",
        type=str,
        default="config.yaml",
        help="Path to configuration file",
    )

    parser.add_argument(
        "--mode",
        type=str,
        choices=["train", "evaluate", "ablation", "all"],
        default="all",
        help="Mode to run",
    )

    parser.add_argument(
        "--temporal_window",
        type=int,
        choices=[1, 3, 5],
        default=None,
        help="Temporal window size to use (if not specified, use all)",
    )

    parser.add_argument(
        "--gnn_type",
        type=str,
        choices=["graphsage", "gatv2", "ecc"],
        default=None,
        help="GNN type to use (if not specified, use the one in config)",
    )

    parser.add_argument(
        "--seed",
        type=int,
        default=None,
        help="Random seed (if not specified, use the one in config)",
    )

    parser.add_argument(
        "--visualize-only",
        action="store_true",
        help="Only generate visualizations without running full evaluation",
    )

    return parser.parse_args()


def main():
    """
    Main function.
    """
    # Parse arguments
    args = parse_args()

    # Load configuration
    config = load_config(args.config)

    # Override configuration with command line arguments
    if args.temporal_window is not None:
        config["data"]["temporal_windows"] = [args.temporal_window]

    if args.gnn_type is not None:
        config["model"]["gnn_type"] = args.gnn_type

    if args.seed is not None:
        config["training"]["seed"] = args.seed

    # Set random seed
    set_seed(config["training"]["seed"])

    # Set up logging
    logger = setup_logging()

    # Create directories
    os.makedirs(config["training"]["checkpoint_dir"], exist_ok=True)
    os.makedirs(config["evaluation"]["visualization"]["save_dir"], exist_ok=True)

    # Save configuration
    save_config(
        config,
        os.path.join(config["training"]["checkpoint_dir"], "config.yaml"),
    )

    # Create data loaders
    logger.info("Creating data loaders...")
    data_loaders = create_data_loaders(config)

    # Run in specified mode
    if args.mode in ["train", "all"]:
        # Train models for each temporal window
        for temporal_window in config["data"]["temporal_windows"]:
            logger.info(f"\nTraining model for temporal window {temporal_window}")

            # Adjust input dimension based on temporal window
            if temporal_window > 1:
                config["model"]["input_dim"] = 10  # Additional temporal feature (9 spatial features + 1 temporal)
            else:
                config["model"]["input_dim"] = 9   # Only spatial features (no temporal)

            # Create model
            model = create_model(config)
            logger.info(f"Model created with {count_parameters(model)} trainable parameters")

            # Create trainer
            trainer = Trainer(config)

            # Train model
            model = trainer.train(
                model,
                data_loaders[f"temporal_{temporal_window}"]["train"],
                data_loaders[f"temporal_{temporal_window}"]["val"],
                temporal_window,
            )

    if args.mode in ["evaluate", "all"]:
        # Evaluate models for each temporal window
        for temporal_window in config["data"]["temporal_windows"]:
            if args.visualize_only:
                logger.info(f"\nGenerating visualizations for temporal window {temporal_window}")
            else:
                logger.info(f"\nEvaluating model for temporal window {temporal_window}")

            # Adjust input dimension based on temporal window
            if temporal_window > 1:
                config["model"]["input_dim"] = 10  # Additional temporal feature (9 spatial features + 1 temporal)
            else:
                config["model"]["input_dim"] = 9   # Only spatial features (no temporal)

            # Create model
            model = create_model(config)

            # Load checkpoint
            checkpoint_path = os.path.join(
                config["training"]["checkpoint_dir"],
                f"model_temporal_{temporal_window}_best.pt"
            )

            if os.path.exists(checkpoint_path):
                checkpoint = torch.load(checkpoint_path)
                model.load_state_dict(checkpoint["model_state_dict"])
                logger.info(f"Loaded checkpoint from {checkpoint_path}")

                # Create evaluator
                evaluator = Evaluator(config)

                # Evaluate model or just generate visualizations
                if args.visualize_only:
                    # Only generate visualizations
                    evaluator.generate_visualizations(
                        model,
                        data_loaders[f"temporal_{temporal_window}"]["test"],
                        temporal_window,
                    )
                    logger.info(f"Visualizations saved to {config['evaluation']['visualization']['save_dir']}")
                else:
                    # Full evaluation with metrics and visualizations
                    metrics = evaluator.evaluate(
                        model,
                        data_loaders[f"temporal_{temporal_window}"]["test"],
                        temporal_window,
                    )
            else:
                logger.warning(f"No checkpoint found at {checkpoint_path}. Skipping evaluation.")

    if args.mode in ["ablation", "all"]:
        # Run ablation studies
        logger.info("\nRunning ablation studies...")

        # Create ablation study
        ablation = AblationStudy(config)

        # Run ablation studies
        ablation.run_gnn_type_ablation()
        ablation.run_temporal_window_ablation()


if __name__ == "__main__":
    main()
