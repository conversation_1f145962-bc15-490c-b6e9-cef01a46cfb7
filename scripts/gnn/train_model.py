#!/usr/bin/env python3
"""
<PERSON>ript to train a GNN model for occupancy prediction and save it to a dedicated folder.
The folder name contains the model type, timestamp, and temporal window.
"""

import os
import sys
import time
import argparse
import yaml
import shutil
import datetime
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train a GNN model for occupancy prediction.')
    parser.add_argument('--gnn_type', type=str, default='graphsage', 
                        choices=['graphsage', 'gatv2', 'ecc'],
                        help='Type of GNN to use (default: graphsage)')
    parser.add_argument('--temporal_window', type=int, default=3,
                        help='Size of temporal window (default: 3)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of epochs to train (default: 100)')
    parser.add_argument('--batch_size', type=int, default=32,
                        help='Batch size for training (default: 32)')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                        help='Learning rate (default: 0.001)')
    parser.add_argument('--output_dir', type=str, default='output',
                        help='Base output directory (default: output)')
    parser.add_argument('--config_path', type=str, default='config.yaml',
                        help='Path to configuration file (default: config.yaml)')
    return parser.parse_args()

def create_output_dir(args):
    """Create output directory with model type, timestamp, and temporal window."""
    # Create timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create directory name
    dir_name = f"{args.gnn_type}_temp{args.temporal_window}_{timestamp}"
    
    # Create full path
    output_path = os.path.join(args.output_dir, dir_name)
    
    # Create directories
    os.makedirs(output_path, exist_ok=True)
    os.makedirs(os.path.join(output_path, "checkpoints"), exist_ok=True)
    os.makedirs(os.path.join(output_path, "visualizations"), exist_ok=True)
    
    logger.info(f"Created output directory: {output_path}")
    return output_path

def update_config(config_path, output_path, args):
    """Update configuration file with new parameters."""
    # Load existing config
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Update config with command line arguments
    config['model']['gnn_type'] = args.gnn_type
    config['data']['temporal_windows'] = [args.temporal_window]
    config['training']['batch_size'] = args.batch_size
    config['training']['learning_rate'] = args.learning_rate
    config['training']['num_epochs'] = args.epochs
    config['training']['checkpoint_dir'] = os.path.join(output_path, "checkpoints")
    config['evaluation']['visualization']['save_dir'] = os.path.join(output_path, "visualizations")
    
    # Adjust input dimension based on temporal window
    if args.temporal_window > 1:
        config['model']['input_dim'] = 10  # 9 spatial features + 1 temporal
    else:
        config['model']['input_dim'] = 9   # Only spatial features
    
    # Save updated config to output directory
    config_output_path = os.path.join(output_path, "config.yaml")
    with open(config_output_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    logger.info(f"Updated configuration saved to: {config_output_path}")
    return config_output_path

def run_training(config_path, args):
    """Run the training process with the specified configuration."""
    # Import here to avoid circular imports
    import main
    
    # Set up command line arguments for main.py
    sys.argv = [
        'main.py',
        '--mode', 'train',
        '--temporal_window', str(args.temporal_window),
        '--gnn_type', args.gnn_type,
        '--config', config_path
    ]
    
    # Run training
    logger.info(f"Starting training with GNN type: {args.gnn_type}, temporal window: {args.temporal_window}")
    main.main()

def main():
    """Main function to run the training script."""
    # Parse command line arguments
    args = parse_args()
    
    # Create output directory
    output_path = create_output_dir(args)
    
    # Update configuration
    config_path = update_config(args.config_path, output_path, args)
    
    # Run training
    run_training(config_path, args)
    
    logger.info(f"Training completed. Model saved to: {output_path}")

if __name__ == "__main__":
    main()
