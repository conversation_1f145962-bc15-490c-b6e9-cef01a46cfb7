# GNN Model Architectures

## 📋 **Complete Architecture Specifications for Occupancy Prediction Models**

This document provides detailed architectural specifications for all Graph Neural Network models evaluated in the occupancy prediction research.

---

## 🏗️ **Model Architecture Overview**

### **Model Categories**
1. **Current Data Models** (9 models)
2. **Historical Data Models** (1 model)
3. **Architecture Types**: GraphSAGE, GATv2 (Standard, Complex, Enhanced), ECC (Edge-Conditioned Convolution, Hybrid)
4. **Temporal Configurations**: 3-frame and 5-frame temporal windows

---

## 🎯 **Current Data Models**

### **1. Complex GATv2 (Temporal 3)**

#### **Architecture Specifications**
```yaml
Model Type: Graph Attention Network v2 (GATv2)
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: 169,601

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   └── Node Features: Point cloud coordinates + temporal features
├── GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 2
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 3
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 4
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── Batch Normalization
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 128
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 86
Training Time: ~4 hours
Early Stopping: Patience 10
Loss Function: Binary Cross Entropy
```

#### **Performance Metrics**
```yaml
Test Accuracy: 72.84%
Precision: 71.05%
Recall: 68.17%
F1 Score: 69.58%
ROC AUC: 79.93%
```

---

### **2. Complex GATv2 (Temporal 5)**

#### **Architecture Specifications**
```yaml
Model Type: Graph Attention Network v2 (GATv2)
Temporal Window: 5 frames
Input Dimension: 10 features
Total Parameters: 169,601

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   └── Extended Temporal Context: 5 consecutive frames
├── GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 2
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 3
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── GATv2 Layer 4
│   ├── Input Dim: 128
│   ├── Hidden Dim: 128
│   ├── Attention Heads: 8
│   ├── Dropout: 0.3
│   ├── Layer Normalization: Yes
│   └── Skip Connections: Yes
├── Batch Normalization
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 128
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 59
Training Time: ~2.5 hours
Early Stopping: Patience 10
Loss Function: Binary Cross Entropy
```

#### **Performance Metrics**
```yaml
Test Accuracy: 70.03%
Precision: 66.93%
Recall: 69.08%
F1 Score: 67.99%
ROC AUC: 77.61%
```

---

### **3. Enhanced GATv2 (Temporal 3)**

#### **Architecture Specifications**
```yaml
Model Type: Enhanced Graph Attention Network v2
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: 6,045,313

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   └── Feature Embedding: 192 dimensions
├── Enhanced GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 192
│   ├── Attention Heads: 8
│   ├── Self-Attention: Yes
│   ├── Residual Connections: Yes
│   ├── Dropout: 0.4
│   └── Layer Normalization: Yes
├── Enhanced GATv2 Layer 2
│   ├── Input Dim: 192
│   ├── Hidden Dim: 192
│   ├── Attention Heads: 8
│   ├── Self-Attention: Yes
│   ├── Residual Connections: Yes
│   ├── Dropout: 0.4
│   └── Layer Normalization: Yes
├── Enhanced GATv2 Layer 3
│   ├── Input Dim: 192
│   ├── Hidden Dim: 192
│   ├── Attention Heads: 8
│   ├── Self-Attention: Yes
│   ├── Residual Connections: Yes
│   ├── Dropout: 0.4
│   └── Layer Normalization: Yes
├── Enhanced GATv2 Layer 4
│   ├── Input Dim: 192
│   ├── Hidden Dim: 192
│   ├── Attention Heads: 8
│   ├── Self-Attention: Yes
│   ├── Residual Connections: Yes
│   ├── Dropout: 0.4
│   └── Layer Normalization: Yes
├── Transformer Components
│   ├── Multi-Head Attention: 8 heads
│   ├── Feed-Forward Network: 512 hidden units
│   └── Positional Encoding: Learned
├── Hierarchical Pooling
│   ├── Local Pooling: Attention-based
│   ├── Global Pooling: Hierarchical aggregation
│   └── Multi-Scale Features: Yes
└── Output Layer
    ├── Input Dim: 192
    ├── Hidden Layers: [128, 64]
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: AdamW
Learning Rate: 0.0005
Weight Decay: 0.01
Batch Size: 16
Epochs Trained: 11 (optimal), 36 (early stopped)
Training Time: ~6 hours
Early Stopping: Patience 15
Loss Function: Focal Loss + Binary Cross Entropy
Gradient Clipping: 1.0
```

#### **Performance Metrics**
```yaml
Test Accuracy: 67.25%
Precision: 60.14%
Recall: 83.46%
F1 Score: 69.90%
ROC AUC: 71.85%
```

---

### **4. GATv2 Standard (Temporal 3)**

#### **Architecture Specifications**
```yaml
Model Type: Standard Graph Attention Network v2
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: ~25,000

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10
│   └── Node Embeddings: 64 dimensions
├── GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── GATv2 Layer 2
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── GATv2 Layer 3
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 64
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 22
Training Time: ~1 hour
Loss Function: Binary Cross Entropy
```

#### **Performance Metrics**
```yaml
Test Accuracy: 66.17%
Precision: 59.08%
Recall: 83.83%
F1 Score: 69.31%
ROC AUC: 69.48%
```

---

### **5. GATv2 (Temporal 5)**

#### **Architecture Specifications**
```yaml
Model Type: Standard Graph Attention Network v2
Temporal Window: 5 frames
Input Dimension: 10 features
Total Parameters: ~30,000

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10
│   ├── Extended Temporal Context: 5 frames
│   └── Node Embeddings: 64 dimensions
├── GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── GATv2 Layer 2
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── GATv2 Layer 3
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.2
│   └── Activation: ReLU
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 64
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 32
Training Time: ~1.5 hours
Loss Function: Binary Cross Entropy
```

#### **Performance Metrics**
```yaml
Test Accuracy: 63.85%
Precision: 57.39%
Recall: 83.59%
F1 Score: 68.06%
ROC AUC: 68.96%
```

---

### **6. GATv2 5-Layer (Temporal 3)**

#### **Architecture Specifications**
```yaml
Model Type: Deep Graph Attention Network v2
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: 51,905

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10
│   └── Node Embeddings: 64 dimensions
├── GATv2 Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.3
│   └── Activation: ReLU
├── GATv2 Layer 2
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.3
│   └── Activation: ReLU
├── GATv2 Layer 3
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.3
│   └── Activation: ReLU
├── GATv2 Layer 4
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.3
│   └── Activation: ReLU
├── GATv2 Layer 5
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Attention Heads: 4
│   ├── Dropout: 0.3
│   └── Activation: ReLU
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 64
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 21
Training Time: ~1 hour
Loss Function: Binary Cross Entropy
Gradient Clipping: 0.5
```

#### **Performance Metrics**
```yaml
Test Accuracy: 57.83%
Precision: 52.09%
Recall: 93.06%
F1 Score: 66.79%
ROC AUC: 64.89%
```

---

### **7. ECC (Edge-Conditioned Convolution) - Temporal 3**

#### **Architecture Specifications**
```yaml
Model Type: Edge-Conditioned Convolution (ECC)
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: 50,390,401

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   └── Node Features: Point cloud coordinates + temporal features
├── ECC Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 64
│   ├── Edge Network: MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.3
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── ECC Layer 2
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Edge Network: MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.3
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── ECC Layer 3
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Edge Network: MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.3
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── Attention Mechanism
│   ├── Attention Heads: 4
│   ├── Multi-Head Attention: Yes
│   └── Spatial Attention: Edge-conditioned
├── Global Pooling: Mean + Max aggregation
└── Output Layer
    ├── Input Dim: 128 (mean+max pooled)
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 16
Epochs Trained: 23 (early stopping)
Training Time: ~30 minutes
Early Stopping: Patience 20
Loss Function: Binary Cross Entropy
Hardware: CUDA GPU (11.6GB)
```

#### **Performance Metrics**
```yaml
Test Accuracy: 60.79%
Precision: 54.49%
Recall: 84.79%
F1 Score: 66.34%
ROC AUC: 66.26%
R² Score: 0.0670
MSE: 0.2314
RMSE: 0.4811
MAE: 0.4734
```

---

### **8. ECC (Edge-Conditioned Convolution) - Temporal 5**

#### **Architecture Specifications**
```yaml
Model Type: Edge-Conditioned Convolution (ECC)
Temporal Window: 5 frames
Input Dimension: 10 features
Total Parameters: 2,106,977

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   ├── Extended Temporal Context: 5 consecutive frames
│   └── Temporal Encoding: Frame position encoding
├── ECC Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 32
│   ├── Edge Network: Lightweight MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.3
│   ├── Batch Normalization: Yes
│   └── Skip Connections: No (memory optimization)
├── ECC Layer 2
│   ├── Input Dim: 32
│   ├── Hidden Dim: 32
│   ├── Edge Network: Lightweight MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.3
│   ├── Batch Normalization: Yes
│   └── Skip Connections: No (memory optimization)
├── Attention Mechanism
│   ├── Attention Heads: 2
│   ├── Lightweight Attention: Memory-optimized
│   └── Temporal-Spatial Fusion: Yes
├── Global Pooling: Mean aggregation (simplified)
└── Output Layer
    ├── Input Dim: 32
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 8 (memory optimization)
Epochs Trained: 84 (early stopping)
Training Time: ~21 minutes
Early Stopping: Patience 20
Loss Function: Binary Cross Entropy
Hardware: CUDA GPU (11.6GB)
Memory Optimization: Reduced parameters for temporal 5
```

#### **Performance Metrics**
```yaml
Test Accuracy: 65.20%
Precision: 59.62%
Recall: 75.82%
F1 Score: 66.75%
ROC AUC: 71.57%
R² Score: 0.1317
MSE: 0.2157
RMSE: 0.4645
MAE: 0.4415
```

---

## 🏛️ **Historical Data Model**

### **9. GraphSAGE (Old Data)**

#### **Architecture Specifications**
```yaml
Model Type: Graph Sample and Aggregate (GraphSAGE)
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: ~15,000

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10
│   └── Node Embeddings: 64 dimensions
├── GraphSAGE Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 64
│   ├── Aggregation: Mean
│   ├── Sampling: Neighbor sampling
│   ├── Dropout: 0.5
│   └── Activation: ReLU
├── GraphSAGE Layer 2
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Aggregation: Mean
│   ├── Sampling: Neighbor sampling
│   ├── Dropout: 0.5
│   └── Activation: ReLU
├── GraphSAGE Layer 3
│   ├── Input Dim: 64
│   ├── Hidden Dim: 64
│   ├── Aggregation: Mean
│   ├── Sampling: Neighbor sampling
│   ├── Dropout: 0.5
│   └── Activation: ReLU
├── Global Pooling: Mean aggregation
└── Output Layer
    ├── Input Dim: 64
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 32
Epochs Trained: 46
Training Time: ~2 hours
Loss Function: Binary Cross Entropy
Data: Historical dataset (higher quality)
```

#### **Performance Metrics**
```yaml
Test Accuracy: 73.04%
Precision: 70.36%
Recall: 89.32%
F1 Score: 78.72%
ROC AUC: 76.13%
```

---

### **9. ECC Hybrid (Edge-Conditioned Convolution) - Temporal 3**

#### **Architecture Specifications**
```yaml
Model Type: ECC Hybrid (Edge-Conditioned Convolution)
Temporal Window: 3 frames
Input Dimension: 10 features
Total Parameters: 15,958,849

Layer Architecture:
├── Input Layer
│   ├── Input Features: 10 (X, Y, Z, Intensity, Temp1-5, Label)
│   └── Node Features: Point cloud coordinates + temporal features
├── ECC Layer 1
│   ├── Input Dim: 10
│   ├── Hidden Dim: 48
│   ├── Edge Network: Hybrid MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.25
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── ECC Layer 2
│   ├── Input Dim: 48
│   ├── Hidden Dim: 48
│   ├── Edge Network: Hybrid MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.25
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── ECC Layer 3
│   ├── Input Dim: 48
│   ├── Hidden Dim: 48
│   ├── Edge Network: Hybrid MLP(edge_features → edge_weights)
│   ├── Node Update: Edge-conditioned message passing
│   ├── Dropout: 0.25
│   ├── Batch Normalization: Yes
│   └── Skip Connections: Yes
├── Attention Mechanism
│   ├── Attention Heads: 3
│   ├── Multi-Head Attention: Yes
│   ├── Spatial Attention: Edge-conditioned
│   └── Layer Normalization: Yes
├── Global Pooling: Mean + Max aggregation (hybrid)
└── Output Layer
    ├── Input Dim: 96 (mean+max pooled)
    ├── Output Dim: 1
    └── Activation: Sigmoid
```

#### **Training Configuration**
```yaml
Optimizer: Adam
Learning Rate: 0.001
Batch Size: 12 (balanced for hybrid model)
Epochs Trained: 21 (early stopping)
Training Time: ~28 minutes
Early Stopping: Patience 20
Loss Function: Binary Cross Entropy
Hardware: CUDA GPU (11.6GB)
```

#### **Performance Metrics**
```yaml
Test Accuracy: 58.06%
Precision: 54.29%
Recall: 50.44%
F1 Score: 52.30%
ROC AUC: 59.94%
R² Score: 0.0205
MSE: 0.2430
RMSE: 0.4929
MAE: 0.4878
```

---

## 📊 **Architecture Comparison Summary**

### **Parameter Efficiency Ranking**
1. **GraphSAGE**: 15K parameters → 78.72% F1 (5.25 F1/1K params)
2. **GATv2 Standard**: 25K parameters → 69.31% F1 (2.77 F1/1K params)
3. **GATv2 (Temp 5)**: 30K parameters → 68.06% F1 (2.27 F1/1K params)
4. **GATv2 5-Layer**: 52K parameters → 66.79% F1 (1.29 F1/1K params)
5. **Complex GATv2**: 169K parameters → 69.58% F1 (0.41 F1/1K params)
6. **ECC (Temp 5)**: 2.1M parameters → 66.75% F1 (0.032 F1/1K params)
7. **Enhanced GATv2**: 6.0M parameters → 69.90% F1 (0.012 F1/1K params)
8. **ECC Hybrid**: 16.0M parameters → 52.30% F1 (0.0033 F1/1K params)
9. **ECC (Temp 3)**: 50.4M parameters → 66.34% F1 (0.0013 F1/1K params)

### **Performance Ranking (ROC AUC)**
1. **Complex GATv2 (Temp 3)**: 79.93% ROC AUC
2. **Complex GATv2 (Temp 5)**: 77.61% ROC AUC
3. **GraphSAGE (Historical)**: 76.13% ROC AUC
4. **Enhanced GATv2**: 71.85% ROC AUC
5. **ECC (Temp 5)**: 71.57% ROC AUC
6. **GATv2 Standard**: 69.48% ROC AUC
7. **GATv2 (Temp 5)**: 68.96% ROC AUC
8. **ECC (Temp 3)**: 66.26% ROC AUC
9. **GATv2 5-Layer**: 64.89% ROC AUC
10. **ECC Hybrid**: 59.94% ROC AUC

### **R² Score Ranking (Regression Performance)**
1. **ECC (Temp 5)**: 0.1317 R²
2. **ECC (Temp 3)**: 0.0670 R²
3. **ECC Hybrid**: 0.0205 R²
4. **Other models**: R² scores not available

### **Architecture Complexity Levels**
- **Simple**: GraphSAGE (3 layers, mean aggregation)
- **Standard**: GATv2 Standard (3 layers, 4 attention heads)
- **Deep**: GATv2 5-Layer (5 layers, increased depth)
- **Complex**: Complex GATv2 (4 layers, 8 heads, normalization)
- **Edge-Conditioned**: ECC (edge-conditioned convolution, spatial relationships)
- **Hybrid**: ECC Hybrid (balanced architecture with skip connections and dual normalization)
- **Advanced**: Enhanced GATv2 (transformer components, hierarchical pooling)

### **Temporal Window Configurations**
- **3-Frame Window**: Focused temporal context, higher accuracy for some models
- **5-Frame Window**: Extended temporal context, better for ECC architecture

### **ECC Architecture Innovations**
- **Edge-Conditioned Learning**: Leverages spatial edge relationships
- **Temporal-Spatial Fusion**: Combines temporal and spatial attention
- **Memory Optimization**: Adaptive architecture for different temporal windows
- **Regression Capability**: First models to provide R² regression metrics

---

**Architecture Documentation**: December 27, 2025
**Total Models**: 10 different architectures
**Parameter Range**: 15K - 50.4M parameters
**Architecture Types**: GraphSAGE, GATv2 (Standard, Complex, Enhanced), ECC (Edge-Conditioned Convolution, Hybrid)
