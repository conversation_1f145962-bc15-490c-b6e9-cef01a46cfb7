#!/usr/bin/env python3

import os
import torch
import torch.nn as nn
import torch.optim as optim
import yaml
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from typing import Dict, List, Tuple, Optional, Union, Any
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    confusion_matrix,
)

from data import create_data_loaders
from model import create_model


class Trainer:
    """
    Trainer class for the occupancy prediction model.
    """

    def __init__(self, config: Dict):
        """
        Initialize the trainer.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.device = torch.device(config["training"]["device"])

        # Set random seed for reproducibility
        torch.manual_seed(config["training"]["seed"])
        np.random.seed(config["training"]["seed"])

        # Create checkpoint directory
        os.makedirs(config["training"]["checkpoint_dir"], exist_ok=True)

        # Initialize metrics history
        self.history = {
            "train_loss": [],
            "val_loss": [],
            "train_accuracy": [],
            "val_accuracy": [],
            "train_precision": [],
            "val_precision": [],
            "train_recall": [],
            "val_recall": [],
            "train_f1": [],
            "val_f1": [],
            "train_roc_auc": [],
            "val_roc_auc": [],
        }

    def train(
        self,
        model: nn.Module,
        train_loader: torch.utils.data.DataLoader,
        val_loader: torch.utils.data.DataLoader,
        temporal_window: int,
    ) -> nn.Module:
        """
        Train the model.

        Args:
            model: Model to train
            train_loader: Training data loader
            val_loader: Validation data loader
            temporal_window: Size of temporal window

        Returns:
            Trained model
        """
        # Move model to device
        model = model.to(self.device)

        # Create optimizer
        optimizer = optim.Adam(
            model.parameters(),
            lr=self.config["training"]["learning_rate"],
            weight_decay=self.config["training"]["weight_decay"],
        )

        # Create learning rate scheduler
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer,
            mode="max",
            factor=self.config["training"]["lr_scheduler"]["factor"],
            patience=self.config["training"]["lr_scheduler"]["patience"],
            min_lr=self.config["training"]["lr_scheduler"]["min_lr"],
        )
        # Print learning rate changes
        print(f"Initial learning rate: {optimizer.param_groups[0]['lr']}")

        # Create loss function
        criterion = nn.BCEWithLogitsLoss()

        # Initialize early stopping variables
        best_val_f1 = 0.0
        patience_counter = 0

        # Training loop
        for epoch in range(self.config["training"]["epochs"]):
            # Train for one epoch
            train_metrics = self._train_epoch(model, train_loader, optimizer, criterion)

            # Evaluate on validation set
            val_metrics = self._evaluate(model, val_loader)

            # Update learning rate
            scheduler.step(val_metrics["f1"])

            # Update history
            for key in train_metrics:
                self.history[f"train_{key}"].append(train_metrics[key])
            for key in val_metrics:
                self.history[f"val_{key}"].append(val_metrics[key])

            # Print progress
            print(f"Epoch {epoch+1}/{self.config['training']['epochs']}")
            print(f"  Train Loss: {train_metrics['loss']:.4f}, "
                  f"Train Accuracy: {train_metrics['accuracy']:.4f}, "
                  f"Train F1: {train_metrics['f1']:.4f}")
            print(f"  Val Loss: {val_metrics['loss']:.4f}, "
                  f"Val Accuracy: {val_metrics['accuracy']:.4f}, "
                  f"Val F1: {val_metrics['f1']:.4f}")

            # Check for improvement
            if val_metrics["f1"] > best_val_f1 + self.config["training"]["early_stopping"]["min_delta"]:
                best_val_f1 = val_metrics["f1"]
                patience_counter = 0

                # Save checkpoint
                checkpoint_path = os.path.join(
                    self.config["training"]["checkpoint_dir"],
                    f"model_temporal_{temporal_window}_best.pt"
                )
                torch.save({
                    "epoch": epoch,
                    "model_state_dict": model.state_dict(),
                    "optimizer_state_dict": optimizer.state_dict(),
                    "scheduler_state_dict": scheduler.state_dict(),
                    "val_f1": val_metrics["f1"],
                    "config": self.config,
                }, checkpoint_path)
                print(f"  Saved checkpoint to {checkpoint_path}")
            else:
                patience_counter += 1
                print(f"  No improvement in validation F1 score. Patience: {patience_counter}/{self.config['training']['early_stopping']['patience']}")

            # Check for early stopping
            if patience_counter >= self.config["training"]["early_stopping"]["patience"]:
                print(f"Early stopping triggered after {epoch+1} epochs")
                break

        # Load best model
        checkpoint_path = os.path.join(
            self.config["training"]["checkpoint_dir"],
            f"model_temporal_{temporal_window}_best.pt"
        )
        checkpoint = torch.load(checkpoint_path)
        model.load_state_dict(checkpoint["model_state_dict"])

        # Plot training history
        self._plot_history(temporal_window)

        return model

    def _train_epoch(
        self,
        model: nn.Module,
        train_loader: torch.utils.data.DataLoader,
        optimizer: torch.optim.Optimizer,
        criterion: nn.Module,
    ) -> Dict[str, float]:
        """
        Train for one epoch.

        Args:
            model: Model to train
            train_loader: Training data loader
            optimizer: Optimizer
            criterion: Loss function

        Returns:
            Dictionary of metrics
        """
        # Set model to training mode
        model.train()

        # Initialize metrics
        total_loss = 0.0
        all_preds = []
        all_targets = []

        # Training loop
        for batch in tqdm(train_loader, desc="Training"):
            # Move batch to device
            batch = batch.to(self.device)

            # Zero gradients
            optimizer.zero_grad()

            # Forward pass
            logits = model(batch.x, batch.edge_index, batch.batch)

            # Ensure logits and targets have the same shape
            if logits.size(0) != batch.y.size(0):
                print(f"Warning: Size mismatch - logits: {logits.size()}, targets: {batch.y.size()}")
                # This is a graph-level prediction, so we need one label per graph
                # Use the majority vote of node labels as the graph label
                graph_labels = []
                for i in range(batch.num_graphs):
                    mask = batch.batch == i
                    graph_label = batch.y[mask].float().mean().round()
                    graph_labels.append(graph_label)
                targets = torch.tensor(graph_labels, device=batch.y.device).float().view(-1, 1)
            else:
                targets = batch.y.float().view(-1, 1)

            # Compute loss
            loss = criterion(logits, targets)

            # Backward pass
            loss.backward()

            # Update weights
            optimizer.step()

            # Update metrics
            total_loss += loss.item() * batch.num_graphs

            # Convert logits to predictions
            preds = torch.sigmoid(logits).detach().cpu().numpy() > 0.5

            # Use the same targets as in the loss calculation
            if logits.size(0) != batch.y.size(0):
                # Use the graph-level targets
                graph_labels = []
                for i in range(batch.num_graphs):
                    mask = batch.batch == i
                    graph_label = batch.y[mask].float().mean().round()
                    graph_labels.append(graph_label)
                targets = torch.tensor(graph_labels).detach().cpu().numpy()
            else:
                targets = batch.y.detach().cpu().numpy()

            all_preds.extend(preds.flatten())
            all_targets.extend(targets.flatten())

        # Compute metrics
        metrics = self._compute_metrics(
            np.array(all_targets),
            np.array(all_preds),
            total_loss / len(train_loader.dataset)
        )

        return metrics

    def _evaluate(
        self,
        model: nn.Module,
        val_loader: torch.utils.data.DataLoader,
    ) -> Dict[str, float]:
        """
        Evaluate the model.

        Args:
            model: Model to evaluate
            val_loader: Validation data loader

        Returns:
            Dictionary of metrics
        """
        # Set model to evaluation mode
        model.eval()

        # Initialize metrics
        total_loss = 0.0
        all_preds = []
        all_targets = []
        all_probs = []

        # Create loss function
        criterion = nn.BCEWithLogitsLoss()

        # Evaluation loop
        with torch.no_grad():
            for batch in tqdm(val_loader, desc="Evaluating"):
                # Move batch to device
                batch = batch.to(self.device)

                # Forward pass
                logits = model(batch.x, batch.edge_index, batch.batch)

                # Ensure logits and targets have the same shape
                if logits.size(0) != batch.y.size(0):
                    # This is a graph-level prediction, so we need one label per graph
                    # Use the majority vote of node labels as the graph label
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels, device=batch.y.device).float().view(-1, 1)
                else:
                    targets = batch.y.float().view(-1, 1)

                # Compute loss
                loss = criterion(logits, targets)

                # Update metrics
                total_loss += loss.item() * batch.num_graphs

                # Convert logits to predictions and probabilities
                probs = torch.sigmoid(logits).cpu().numpy()
                preds = probs > 0.5

                # Use the same targets as in the loss calculation
                if logits.size(0) != batch.y.size(0):
                    # Use the graph-level targets
                    graph_labels = []
                    for i in range(batch.num_graphs):
                        mask = batch.batch == i
                        graph_label = batch.y[mask].float().mean().round()
                        graph_labels.append(graph_label)
                    targets = torch.tensor(graph_labels).cpu().numpy()
                else:
                    targets = batch.y.cpu().numpy()

                all_probs.extend(probs.flatten())
                all_preds.extend(preds.flatten())
                all_targets.extend(targets.flatten())

        # Compute metrics
        metrics = self._compute_metrics(
            np.array(all_targets),
            np.array(all_preds),
            total_loss / len(val_loader.dataset),
            np.array(all_probs)
        )

        return metrics

    def _compute_metrics(
        self,
        targets: np.ndarray,
        preds: np.ndarray,
        loss: float,
        probs: Optional[np.ndarray] = None,
    ) -> Dict[str, float]:
        """
        Compute evaluation metrics.

        Args:
            targets: Ground truth labels
            preds: Predicted labels
            loss: Loss value
            probs: Predicted probabilities

        Returns:
            Dictionary of metrics
        """
        metrics = {
            "loss": loss,
            "accuracy": accuracy_score(targets, preds),
            "precision": precision_score(targets, preds, zero_division=0),
            "recall": recall_score(targets, preds, zero_division=0),
            "f1": f1_score(targets, preds, zero_division=0),
        }

        # Compute ROC AUC if probabilities are provided
        if probs is not None:
            metrics["roc_auc"] = roc_auc_score(targets, probs) if len(np.unique(targets)) > 1 else 0.5

        return metrics

    def _plot_history(self, temporal_window: int):
        """
        Plot training history.

        Args:
            temporal_window: Size of temporal window
        """
        # Create figure
        fig, axs = plt.subplots(2, 2, figsize=(15, 10))

        # Plot loss
        axs[0, 0].plot(self.history["train_loss"], label="Train")
        axs[0, 0].plot(self.history["val_loss"], label="Validation")
        axs[0, 0].set_title("Loss")
        axs[0, 0].set_xlabel("Epoch")
        axs[0, 0].set_ylabel("Loss")
        axs[0, 0].legend()

        # Plot accuracy
        axs[0, 1].plot(self.history["train_accuracy"], label="Train")
        axs[0, 1].plot(self.history["val_accuracy"], label="Validation")
        axs[0, 1].set_title("Accuracy")
        axs[0, 1].set_xlabel("Epoch")
        axs[0, 1].set_ylabel("Accuracy")
        axs[0, 1].legend()

        # Plot F1 score
        axs[1, 0].plot(self.history["train_f1"], label="Train")
        axs[1, 0].plot(self.history["val_f1"], label="Validation")
        axs[1, 0].set_title("F1 Score")
        axs[1, 0].set_xlabel("Epoch")
        axs[1, 0].set_ylabel("F1 Score")
        axs[1, 0].legend()

        # Plot ROC AUC
        if "train_roc_auc" in self.history and len(self.history["train_roc_auc"]) > 0:
            axs[1, 1].plot(self.history["train_roc_auc"], label="Train")
            axs[1, 1].plot(self.history["val_roc_auc"], label="Validation")
            axs[1, 1].set_title("ROC AUC")
            axs[1, 1].set_xlabel("Epoch")
            axs[1, 1].set_ylabel("ROC AUC")
            axs[1, 1].legend()

        # Save figure
        plt.tight_layout()
        plt.savefig(os.path.join(
            self.config["training"]["checkpoint_dir"],
            f"history_temporal_{temporal_window}.png"
        ))
        plt.close()


if __name__ == "__main__":
    # Load configuration
    with open("config.yaml", "r") as f:
        config = yaml.safe_load(f)

    # Create data loaders
    data_loaders = create_data_loaders(config)

    # Create trainer
    trainer = Trainer(config)

    # Train models for each temporal window
    for temporal_window in config["data"]["temporal_windows"]:
        print(f"\nTraining model for temporal window {temporal_window}")

        # Adjust input dimension based on temporal window
        if temporal_window > 1:
            config["model"]["input_dim"] = 14  # Additional temporal feature
        else:
            config["model"]["input_dim"] = 13

        # Create model
        model = create_model(config)

        # Train model
        model = trainer.train(
            model,
            data_loaders[f"temporal_{temporal_window}"]["train"],
            data_loaders[f"temporal_{temporal_window}"]["val"],
            temporal_window,
        )
