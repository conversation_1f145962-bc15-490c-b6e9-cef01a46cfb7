#!/usr/bin/env python3

"""
Visualize Results

This script provides a command-line interface for visualizing results from
the collaborative perception pipeline.

Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
Date: 2025
"""

import os
import argparse
import logging
import glob
from datetime import datetime
import matplotlib.pyplot as plt
import json
import sys
import numpy as np
import pandas as pd


def setup_logging(log_dir=None, verbose=False):
    """Set up logging configuration"""
    log_level = logging.DEBUG if verbose else logging.INFO
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    
    if log_dir:
        os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"visualize_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        handlers = [
            logging.StreamHandler(),
            logging.FileHandler(log_file)
        ]
    else:
        handlers = [logging.StreamHandler()]
    
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=handlers
    )
    
    return logging.getLogger("VisualizeResults")


def visualize_point_clouds(input_dir, output_dir, logger):
    """Visualize point clouds"""
    logger.info("Visualizing point clouds")
    
    # Find transformed data files
    transformed_files = glob.glob(os.path.join(input_dir, '**', 'transformed_*.csv'), recursive=True)
    
    if not transformed_files:
        logger.error("No transformed data files found")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Import visualization module
    try:
        from collaborative_perception.visualization.global_map import GlobalMapVisualizer
        
        for file in transformed_files:
            logger.info(f"Visualizing {file}")
            
            # Create visualizer
            visualizer = GlobalMapVisualizer()
            
            # Load data
            if not visualizer.load_data(file):
                logger.error(f"Failed to load data from {file}")
                continue
            
            # Create 2D visualization
            fig_2d = visualizer.visualize('2d', analyze=True)
            if fig_2d:
                output_file = os.path.join(output_dir, f"{os.path.basename(file).replace('.csv', '_2d.png')}")
                fig_2d.savefig(output_file, dpi=300, bbox_inches='tight')
                logger.info(f"Saved 2D visualization to {output_file}")
                plt.close(fig_2d)
            
            # Create 3D visualization
            fig_3d = visualizer.visualize('3d')
            if fig_3d:
                output_file = os.path.join(output_dir, f"{os.path.basename(file).replace('.csv', '_3d.png')}")
                fig_3d.savefig(output_file, dpi=300, bbox_inches='tight')
                logger.info(f"Saved 3D visualization to {output_file}")
                plt.close(fig_3d)
            
            # Create density visualization
            fig_density = visualizer.visualize('density')
            if fig_density:
                output_file = os.path.join(output_dir, f"{os.path.basename(file).replace('.csv', '_density.png')}")
                fig_density.savefig(output_file, dpi=300, bbox_inches='tight')
                logger.info(f"Saved density visualization to {output_file}")
                plt.close(fig_density)
        
        return True
        
    except ImportError as e:
        logger.error(f"Error importing visualization module: {e}")
        return False


def visualize_annotations(input_dir, output_dir, logger):
    """Visualize annotated point clouds"""
    logger.info("Visualizing annotations")
    
    # Find annotated data files
    annotated_files = glob.glob(os.path.join(input_dir, '**', 'annotated_*.csv'), recursive=True)
    
    if not annotated_files:
        logger.error("No annotated data files found")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Import visualization module
    try:
        from collaborative_perception.annotation.annotator import PointCloudAnnotator
        
        for file in annotated_files:
            logger.info(f"Visualizing {file}")
            
            # Create annotator
            annotator = PointCloudAnnotator()
            
            # Load data
            if not annotator.load_data(file):
                logger.error(f"Failed to load data from {file}")
                continue
            
            # Create 2D visualization
            fig_2d = annotator.visualize_annotations('2d')
            if fig_2d:
                output_file = os.path.join(output_dir, f"{os.path.basename(file).replace('.csv', '_2d.png')}")
                fig_2d.savefig(output_file, dpi=300, bbox_inches='tight')
                logger.info(f"Saved 2D visualization to {output_file}")
                plt.close(fig_2d)
            
            # Create 3D visualization
            fig_3d = annotator.visualize_annotations('3d')
            if fig_3d:
                output_file = os.path.join(output_dir, f"{os.path.basename(file).replace('.csv', '_3d.png')}")
                fig_3d.savefig(output_file, dpi=300, bbox_inches='tight')
                logger.info(f"Saved 3D visualization to {output_file}")
                plt.close(fig_3d)
        
        return True
        
    except ImportError as e:
        logger.error(f"Error importing annotation module: {e}")
        return False


def create_animations(input_dir, output_dir, logger):
    """Create animations of point cloud data"""
    logger.info("Creating animations")
    
    # Find directories containing transformed data files
    data_dirs = []
    for root, dirs, files in os.walk(input_dir):
        if any(file.startswith('transformed_') and file.endswith('.csv') for file in files):
            data_dirs.append(root)
    
    if not data_dirs:
        logger.error("No directories with transformed data files found")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Import animation module
    try:
        from collaborative_perception.visualization.animator import Animator
        
        for data_dir in data_dirs:
            logger.info(f"Creating animation for {data_dir}")
            
            # Create animator
            animator = Animator(data_dir)
            
            # Load data frames
            if not animator.load_data_frames(max_frames=100):
                logger.error(f"Failed to load data frames from {data_dir}")
                continue
            
            # Create 2D animation
            output_file = os.path.join(output_dir, f"{os.path.basename(data_dir)}_2d.mp4")
            animation = animator.create_animation_2d(output_file, fps=10, dpi=100)
            if not animation:
                logger.error(f"Failed to create 2D animation for {data_dir}")
            
            # Create 3D animation
            output_file = os.path.join(output_dir, f"{os.path.basename(data_dir)}_3d.mp4")
            animation = animator.create_animation_3d(output_file, fps=10, dpi=100)
            if not animation:
                logger.error(f"Failed to create 3D animation for {data_dir}")
        
        return True
        
    except ImportError as e:
        logger.error(f"Error importing animation module: {e}")
        return False


def visualize_evaluation_results(input_dir, output_dir, logger):
    """Visualize evaluation results"""
    logger.info("Visualizing evaluation results")
    
    # Find evaluation result files
    evaluation_files = glob.glob(os.path.join(input_dir, '**', '*evaluation.json'), recursive=True)
    
    if not evaluation_files:
        logger.error("No evaluation result files found")
        return False
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load evaluation results
    results = []
    for file in evaluation_files:
        try:
            with open(file, 'r') as f:
                result = json.load(f)
                result['file'] = file
                result['scenario'] = os.path.basename(os.path.dirname(os.path.dirname(file)))
                results.append(result)
        except Exception as e:
            logger.error(f"Error loading evaluation results from {file}: {e}")
    
    if not results:
        logger.error("No evaluation results loaded")
        return False
    
    # Create summary table
    summary_data = []
    for result in results:
        summary_data.append({
            'Scenario': result['scenario'],
            'RMSE': result.get('rmse', np.nan),
            'IoU': result.get('iou', np.nan),
            'Precision': result.get('precision', np.nan),
            'Recall': result.get('recall', np.nan),
            'F1-score': result.get('f1_score', np.nan)
        })
    
    summary_df = pd.DataFrame(summary_data)
    
    # Save summary table
    summary_file = os.path.join(output_dir, 'evaluation_summary.csv')
    summary_df.to_csv(summary_file, index=False)
    logger.info(f"Saved evaluation summary to {summary_file}")
    
    # Create summary plots
    try:
        # RMSE by scenario
        if not summary_df['RMSE'].isna().all():
            fig, ax = plt.subplots(figsize=(10, 6))
            summary_df.plot(x='Scenario', y='RMSE', kind='bar', ax=ax)
            ax.set_ylabel('RMSE (m)')
            ax.set_title('Root Mean Square Error by Scenario')
            ax.grid(True, alpha=0.3)
            
            output_file = os.path.join(output_dir, 'rmse_by_scenario.png')
            fig.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"Saved RMSE plot to {output_file}")
            plt.close(fig)
        
        # IoU by scenario
        if not summary_df['IoU'].isna().all():
            fig, ax = plt.subplots(figsize=(10, 6))
            summary_df.plot(x='Scenario', y='IoU', kind='bar', ax=ax)
            ax.set_ylabel('IoU')
            ax.set_title('Intersection over Union by Scenario')
            ax.grid(True, alpha=0.3)
            
            output_file = os.path.join(output_dir, 'iou_by_scenario.png')
            fig.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"Saved IoU plot to {output_file}")
            plt.close(fig)
        
        # F1-score by scenario
        if not summary_df['F1-score'].isna().all():
            fig, ax = plt.subplots(figsize=(10, 6))
            summary_df.plot(x='Scenario', y=['Precision', 'Recall', 'F1-score'], kind='bar', ax=ax)
            ax.set_ylabel('Score')
            ax.set_title('Precision, Recall, and F1-score by Scenario')
            ax.grid(True, alpha=0.3)
            ax.legend()
            
            output_file = os.path.join(output_dir, 'f1_by_scenario.png')
            fig.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"Saved F1-score plot to {output_file}")
            plt.close(fig)
        
        return True
        
    except Exception as e:
        logger.error(f"Error creating summary plots: {e}")
        return False


def main():
    """Main function to visualize results."""
    parser = argparse.ArgumentParser(description='Visualize results from the collaborative perception pipeline')
    parser.add_argument('--input-dir', '-i', type=str, required=True,
                        help='Input directory containing results')
    parser.add_argument('--output-dir', '-o', type=str, default=None,
                        help='Output directory for visualizations')
    parser.add_argument('--visualize-point-clouds', action='store_true',
                        help='Visualize point clouds')
    parser.add_argument('--visualize-annotations', action='store_true',
                        help='Visualize annotated point clouds')
    parser.add_argument('--create-animations', action='store_true',
                        help='Create animations of point cloud data')
    parser.add_argument('--visualize-evaluation', action='store_true',
                        help='Visualize evaluation results')
    parser.add_argument('--all', '-a', action='store_true',
                        help='Perform all visualizations')
    parser.add_argument('--verbose', '-v', action='store_true',
                        help='Enable verbose output')
    
    args = parser.parse_args()
    
    # Set default output directory if not specified
    if not args.output_dir:
        args.output_dir = os.path.join(args.input_dir, 'visualizations')
    
    # Set up logging
    log_dir = os.path.join(args.output_dir, 'logs')
    logger = setup_logging(log_dir, args.verbose)
    
    # Determine which visualizations to perform
    if args.all:
        args.visualize_point_clouds = True
        args.visualize_annotations = True
        args.create_animations = True
        args.visualize_evaluation = True
    
    if not (args.visualize_point_clouds or args.visualize_annotations or args.create_animations or args.visualize_evaluation):
        logger.error("No visualizations specified")
        parser.print_help()
        return 1
    
    # Perform visualizations
    success = True
    
    if args.visualize_point_clouds:
        point_clouds_dir = os.path.join(args.output_dir, 'point_clouds')
        if not visualize_point_clouds(args.input_dir, point_clouds_dir, logger):
            logger.error("Failed to visualize point clouds")
            success = False
    
    if args.visualize_annotations:
        annotations_dir = os.path.join(args.output_dir, 'annotations')
        if not visualize_annotations(args.input_dir, annotations_dir, logger):
            logger.error("Failed to visualize annotations")
            success = False
    
    if args.create_animations:
        animations_dir = os.path.join(args.output_dir, 'animations')
        if not create_animations(args.input_dir, animations_dir, logger):
            logger.error("Failed to create animations")
            success = False
    
    if args.visualize_evaluation:
        evaluation_dir = os.path.join(args.output_dir, 'evaluation')
        if not visualize_evaluation_results(args.input_dir, evaluation_dir, logger):
            logger.error("Failed to visualize evaluation results")
            success = False
    
    if success:
        logger.info("Successfully visualized results")
        return 0
    else:
        logger.error("Failed to visualize some results")
        return 1


if __name__ == '__main__':
    sys.exit(main())
