#!/usr/bin/env python3

import os
import argparse
import pandas as pd
import numpy as np
import torch
from torch_geometric.data import Data
from tqdm import tqdm
import logging
from sklearn.neighbors import NearestNeighbors
import matplotlib.pyplot as plt
from matplotlib.colors import ListedColormap
import matplotlib.patches as mpatches
from collections import defaultdict

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define label mapping
LABEL_MAPPING = {
    'unknown': 0,
    'workstation': 1,
    'robot': 2,
    'boundary': 3
}

# Define color mapping for visualization
COLOR_MAPPING = {
    0: 'gray',    # unknown
    1: 'green',   # workstation
    2: 'yellow',  # robot
    3: 'violet'   # boundary
}

# Define temporal window sizes
TEMPORAL_WINDOW_SIZES = [1, 3, 5]  # 1 means no temporal window (single frame)

def voxelize_point_cloud(points_df, voxel_size=0.1):
    """
    Voxelize point cloud data.
    
    Args:
        points_df: DataFrame with point cloud data
        voxel_size: Size of voxels
        
    Returns:
        Dictionary of voxels with points and DataFrame with voxel information
    """
    # Extract point coordinates
    point_coords = []
    
    # Process robot 1 points
    if 'robot_1_global_x_radar' in points_df.columns:
        robot1_mask = points_df['robot_1_global_x_radar'].notna()
        if robot1_mask.any():
            robot1_points = points_df[robot1_mask]
            robot1_coords = robot1_points[['robot_1_global_x_radar', 'robot_1_global_y_radar', 'robot_1_global_z_radar']].values
            robot1_snr = robot1_points['robot_1_global_snr_radar'].values
            robot1_labels = robot1_points['annotation_general'].values
            
            for i in range(len(robot1_coords)):
                point_coords.append({
                    'x': robot1_coords[i, 0],
                    'y': robot1_coords[i, 1],
                    'z': robot1_coords[i, 2],
                    'snr': robot1_snr[i],
                    'label': robot1_labels[i],
                    'robot_id': 1
                })
    
    # Process robot 2 points
    if 'robot_2_global_x_radar' in points_df.columns:
        robot2_mask = points_df['robot_2_global_x_radar'].notna()
        if robot2_mask.any():
            robot2_points = points_df[robot2_mask]
            robot2_coords = robot2_points[['robot_2_global_x_radar', 'robot_2_global_y_radar', 'robot_2_global_z_radar']].values
            robot2_snr = robot2_points['robot_2_global_snr_radar'].values
            robot2_labels = robot2_points['annotation_general'].values
            
            for i in range(len(robot2_coords)):
                point_coords.append({
                    'x': robot2_coords[i, 0],
                    'y': robot2_coords[i, 1],
                    'z': robot2_coords[i, 2],
                    'snr': robot2_snr[i],
                    'label': robot2_labels[i],
                    'robot_id': 2
                })
    
    # Convert to DataFrame
    points_data = pd.DataFrame(point_coords)
    
    if len(points_data) == 0:
        logger.warning("No valid points found in frame")
        return {}, pd.DataFrame()
    
    # Compute voxel indices
    points_data['voxel_x'] = np.floor(points_data['x'] / voxel_size).astype(int)
    points_data['voxel_y'] = np.floor(points_data['y'] / voxel_size).astype(int)
    points_data['voxel_z'] = np.floor(points_data['z'] / voxel_size).astype(int)
    points_data['voxel_id'] = points_data.apply(
        lambda row: f"{row['voxel_x']}_{row['voxel_y']}_{row['voxel_z']}", axis=1
    )
    
    # Group by voxel
    voxels = {}
    voxel_data = []
    
    for voxel_id, group in points_data.groupby('voxel_id'):
        # Calculate voxel center
        voxel_center = [
            (group['voxel_x'].iloc[0] + 0.5) * voxel_size,
            (group['voxel_y'].iloc[0] + 0.5) * voxel_size,
            (group['voxel_z'].iloc[0] + 0.5) * voxel_size
        ]
        
        # Determine voxel label (majority vote)
        label_counts = group['label'].value_counts()
        majority_label = label_counts.idxmax()
        
        # Calculate mean SNR
        mean_snr = group['snr'].mean()
        
        # Store voxel data
        voxels[voxel_id] = {
            'center': voxel_center,
            'points': group,
            'label': majority_label,
            'num_points': len(group),
            'mean_snr': mean_snr
        }
        
        # Add to voxel DataFrame
        voxel_data.append({
            'voxel_id': voxel_id,
            'center_x': voxel_center[0],
            'center_y': voxel_center[1],
            'center_z': voxel_center[2],
            'label': majority_label,
            'num_points': len(group),
            'mean_snr': mean_snr
        })
    
    voxel_df = pd.DataFrame(voxel_data)
    return voxels, voxel_df

def create_node_features(voxel_df, frame_df):
    """
    Create node features for each voxel.
    
    Args:
        voxel_df: DataFrame with voxel information
        frame_df: Original frame DataFrame
        
    Returns:
        Node features array, node positions array, node labels array
    """
    # Extract robot positions
    robot_positions = {}
    if 'robot_1_global_x' in frame_df.columns and frame_df['robot_1_global_x'].notna().any():
        robot_positions['robot_1'] = frame_df[['robot_1_global_x', 'robot_1_global_y', 'robot_1_global_z']].dropna().iloc[0].values
    else:
        robot_positions['robot_1'] = np.array([0, 0, 0])
        
    if 'robot_2_global_x' in frame_df.columns and frame_df['robot_2_global_x'].notna().any():
        robot_positions['robot_2'] = frame_df[['robot_2_global_x', 'robot_2_global_y', 'robot_2_global_z']].dropna().iloc[0].values
    else:
        robot_positions['robot_2'] = np.array([0, 0, 0])
    
    # Create node features
    node_features = []
    node_positions = []
    node_labels = []
    
    for _, voxel in voxel_df.iterrows():
        # Position features
        pos = [voxel['center_x'], voxel['center_y'], voxel['center_z']]
        
        # Distance to robots
        dist_to_robot1 = np.linalg.norm(np.array(pos) - robot_positions['robot_1'])
        dist_to_robot2 = np.linalg.norm(np.array(pos) - robot_positions['robot_2'])
        
        # Create feature vector
        features = [
            voxel['center_x'],  # X coordinate
            voxel['center_y'],  # Y coordinate
            voxel['center_z'],  # Z coordinate
            voxel['num_points'],  # Number of points in voxel
            voxel['mean_snr'],  # Mean SNR of points in voxel
            dist_to_robot1,  # Distance to robot 1
            dist_to_robot2,  # Distance to robot 2
            robot_positions['robot_1'][0],  # Robot 1 X
            robot_positions['robot_1'][1],  # Robot 1 Y
            robot_positions['robot_1'][2],  # Robot 1 Z
            robot_positions['robot_2'][0],  # Robot 2 X
            robot_positions['robot_2'][1],  # Robot 2 Y
            robot_positions['robot_2'][2],  # Robot 2 Z
        ]
        
        # Add to lists
        node_features.append(features)
        node_positions.append(pos)
        
        # Convert label to numeric
        label = LABEL_MAPPING.get(voxel['label'], 0)
        node_labels.append(label)
    
    return np.array(node_features), np.array(node_positions), np.array(node_labels)

def create_edge_index(node_positions, k=6):
    """
    Create edge connections using k-nearest neighbors.
    
    Args:
        node_positions: Positions of nodes
        k: Number of neighbors
        
    Returns:
        Edge index array
    """
    if len(node_positions) <= 1:
        return np.zeros((2, 0), dtype=np.int64)
    
    # Use k+1 because each point is its own nearest neighbor
    k = min(k + 1, len(node_positions))
    
    # Find k nearest neighbors
    nbrs = NearestNeighbors(n_neighbors=k, algorithm='ball_tree').fit(node_positions)
    distances, indices = nbrs.kneighbors(node_positions)
    
    # Create edge index
    edge_index = []
    for i, neighbors in enumerate(indices):
        for j in neighbors[1:]:  # Skip the first neighbor (self)
            edge_index.append([i, j])
    
    # Convert to numpy array and transpose
    edge_index = np.array(edge_index).T
    return edge_index

def visualize_frame(voxel_df, edge_index, output_path, title=None):
    """
    Visualize a frame as a 2D scatter plot with edges.
    
    Args:
        voxel_df: DataFrame with voxel information
        edge_index: Edge connections
        output_path: Path to save the visualization
        title: Optional title for the plot
    """
    plt.figure(figsize=(10, 8))
    
    # Create scatter plot
    colors = [COLOR_MAPPING.get(LABEL_MAPPING.get(label, 0), 'gray') for label in voxel_df['label']]
    plt.scatter(voxel_df['center_x'], voxel_df['center_y'], c=colors, s=30, alpha=0.7)
    
    # Draw edges
    if edge_index.shape[1] > 0:
        for i in range(edge_index.shape[1]):
            src, dst = edge_index[:, i]
            plt.plot(
                [voxel_df.iloc[src]['center_x'], voxel_df.iloc[dst]['center_x']],
                [voxel_df.iloc[src]['center_y'], voxel_df.iloc[dst]['center_y']],
                'k-', alpha=0.1, linewidth=0.5
            )
    
    # Set plot limits
    plt.xlim(-10, 10)
    plt.ylim(-5, 5)
    
    # Add legend
    legend_elements = [
        mpatches.Patch(color='gray', label='Unknown'),
        mpatches.Patch(color='green', label='Workstation'),
        mpatches.Patch(color='yellow', label='Robot'),
        mpatches.Patch(color='violet', label='Boundary')
    ]
    plt.legend(handles=legend_elements, loc='upper right')
    
    # Add title
    if title:
        plt.title(title)
    
    # Add labels
    plt.xlabel('X (m)')
    plt.ylabel('Y (m)')
    
    # Add grid
    plt.grid(True, alpha=0.3)
    
    # Save figure
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

def process_frame(frame_df, voxel_size=0.1, k_neighbors=6, visualize=False, vis_dir=None):
    """
    Process a single frame of point cloud data.
    
    Args:
        frame_df: DataFrame with point cloud data for a single timestamp
        voxel_size: Size of voxels
        k_neighbors: Number of neighbors for edge connections
        visualize: Whether to visualize the frame
        vis_dir: Directory to save visualizations
        
    Returns:
        PyG Data object, voxel DataFrame, statistics
    """
    timestamp = frame_df['vicon_timestamp'].iloc[0]
    
    # Voxelize point cloud
    voxels, voxel_df = voxelize_point_cloud(frame_df, voxel_size)
    
    if len(voxel_df) == 0:
        logger.warning(f"No voxels created for timestamp {timestamp}")
        # Create empty data object
        data = Data(
            x=torch.zeros((0, 13), dtype=torch.float),
            edge_index=torch.zeros((2, 0), dtype=torch.long),
            y=torch.zeros(0, dtype=torch.long),
            pos=torch.zeros((0, 3), dtype=torch.float),
            timestamp=timestamp
        )
        
        stats = {
            'timestamp': timestamp,
            'num_nodes': 0,
            'num_edges': 0,
            'label_distribution': {label: 0 for label in LABEL_MAPPING.values()}
        }
        
        return data, voxel_df, stats
    
    # Create node features
    node_features, node_positions, node_labels = create_node_features(voxel_df, frame_df)
    
    # Create edge connections
    edge_index = create_edge_index(node_positions, k_neighbors)
    
    # Create PyG Data object
    data = Data(
        x=torch.tensor(node_features, dtype=torch.float),
        edge_index=torch.tensor(edge_index, dtype=torch.long),
        y=torch.tensor(node_labels, dtype=torch.long),
        pos=torch.tensor(node_positions, dtype=torch.float),
        timestamp=timestamp
    )
    
    # Visualize frame if requested
    if visualize and vis_dir:
        os.makedirs(vis_dir, exist_ok=True)
        vis_path = os.path.join(vis_dir, f"frame_{timestamp:.2f}.png")
        visualize_frame(voxel_df, edge_index, vis_path, title=f"Frame at t={timestamp:.2f}")
    
    # Calculate statistics
    label_distribution = {label_id: (data.y == label_id).sum().item() for label, label_id in LABEL_MAPPING.items()}
    
    stats = {
        'timestamp': timestamp,
        'num_nodes': data.num_nodes,
        'num_edges': data.num_edges,
        'label_distribution': label_distribution
    }
    
    return data, voxel_df, stats

def create_temporal_frame(frames, window_size, k_neighbors=6, visualize=False, vis_dir=None):
    """
    Create a temporal frame by aggregating multiple frames.
    
    Args:
        frames: List of (data, voxel_df, stats) tuples
        window_size: Size of temporal window
        k_neighbors: Number of neighbors for edge connections
        visualize: Whether to visualize the frame
        vis_dir: Directory to save visualizations
        
    Returns:
        PyG Data object, statistics
    """
    if len(frames) != window_size:
        logger.warning(f"Expected {window_size} frames, got {len(frames)}")
        return None, None
    
    # Get center frame timestamp
    center_idx = window_size // 2
    center_timestamp = frames[center_idx][0].timestamp
    
    # Collect all node features and positions
    all_features = []
    all_positions = []
    all_labels = []
    
    for i, (data, _, _) in enumerate(frames):
        # Skip empty frames
        if data.num_nodes == 0:
            continue
        
        # Get features, positions, and labels
        features = data.x.numpy()
        positions = data.pos.numpy()
        labels = data.y.numpy()
        
        # Add temporal offset feature
        temporal_offset = i - center_idx  # Relative position in window
        temporal_features = np.ones((features.shape[0], 1)) * temporal_offset
        
        # Concatenate features with temporal offset
        features_with_time = np.hstack([features, temporal_features])
        
        # Add to lists
        all_features.append(features_with_time)
        all_positions.append(positions)
        all_labels.append(labels)
    
    # Combine all features, positions, and labels
    if not all_features:
        logger.warning(f"No valid frames in temporal window for timestamp {center_timestamp}")
        # Create empty data object
        data = Data(
            x=torch.zeros((0, 14), dtype=torch.float),  # 13 features + 1 temporal
            edge_index=torch.zeros((2, 0), dtype=torch.long),
            y=torch.zeros(0, dtype=torch.long),
            pos=torch.zeros((0, 3), dtype=torch.float),
            timestamp=center_timestamp
        )
        
        stats = {
            'timestamp': center_timestamp,
            'num_nodes': 0,
            'num_edges': 0,
            'label_distribution': {label: 0 for label in LABEL_MAPPING.values()},
            'temporal_window_size': window_size
        }
        
        return data, stats
    
    combined_features = np.vstack(all_features)
    combined_positions = np.vstack(all_positions)
    combined_labels = np.concatenate(all_labels)
    
    # Create edge connections
    edge_index = create_edge_index(combined_positions, k_neighbors)
    
    # Create PyG Data object
    data = Data(
        x=torch.tensor(combined_features, dtype=torch.float),
        edge_index=torch.tensor(edge_index, dtype=torch.long),
        y=torch.tensor(combined_labels, dtype=torch.long),
        pos=torch.tensor(combined_positions, dtype=torch.float),
        timestamp=center_timestamp
    )
    
    # Visualize frame if requested
    if visualize and vis_dir:
        os.makedirs(vis_dir, exist_ok=True)
        
        # Create a DataFrame for visualization
        vis_df = pd.DataFrame({
            'center_x': combined_positions[:, 0],
            'center_y': combined_positions[:, 1],
            'center_z': combined_positions[:, 2],
            'label': [list(LABEL_MAPPING.keys())[list(LABEL_MAPPING.values()).index(l)] for l in combined_labels]
        })
        
        vis_path = os.path.join(vis_dir, f"temporal_{window_size}_frame_{center_timestamp:.2f}.png")
        visualize_frame(vis_df, edge_index, vis_path, 
                        title=f"Temporal Frame (window={window_size}) at t={center_timestamp:.2f}")
    
    # Calculate statistics
    label_distribution = {label_id: (data.y == label_id).sum().item() for label, label_id in LABEL_MAPPING.items()}
    
    stats = {
        'timestamp': center_timestamp,
        'num_nodes': data.num_nodes,
        'num_edges': data.num_edges,
        'label_distribution': label_distribution,
        'temporal_window_size': window_size
    }
    
    return data, stats

def process_csv_file(csv_filepath, output_dir, voxel_size=0.1, k_neighbors=6, visualize=False):
    """
    Process a single CSV file and convert to GNN frames.
    
    Args:
        csv_filepath: Path to annotated CSV file
        output_dir: Directory to save output files
        voxel_size: Size of voxels
        k_neighbors: Number of neighbors for edge connections
        visualize: Whether to visualize frames
        
    Returns:
        List of statistics for processed frames
    """
    logger.info(f"Processing file: {csv_filepath}")
    
    # Load CSV file
    df = pd.read_csv(csv_filepath)
    logger.info(f"Loaded {len(df)} points")
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    
    # Create visualization directory
    vis_dir = os.path.join(output_dir, 'visualizations') if visualize else None
    if vis_dir:
        os.makedirs(vis_dir, exist_ok=True)
    
    # Get unique timestamps
    timestamps = sorted(df['vicon_timestamp'].unique())
    logger.info(f"Found {len(timestamps)} unique timestamps")
    
    # Process each timestamp (non-temporal)
    frame_stats = []
    processed_frames = []
    
    # Create directory for non-temporal frames
    non_temporal_dir = os.path.join(output_dir, 'temporal_1')
    os.makedirs(non_temporal_dir, exist_ok=True)
    
    # Create visualization directory for non-temporal frames
    non_temporal_vis_dir = os.path.join(vis_dir, 'temporal_1') if vis_dir else None
    if non_temporal_vis_dir:
        os.makedirs(non_temporal_vis_dir, exist_ok=True)
    
    logger.info("Processing non-temporal frames...")
    for timestamp in tqdm(timestamps, desc="Processing frames"):
        # Get data for this timestamp
        frame_df = df[df['vicon_timestamp'] == timestamp]
        
        # Process frame
        data, voxel_df, stats = process_frame(
            frame_df, voxel_size, k_neighbors, 
            visualize=visualize, vis_dir=non_temporal_vis_dir
        )
        
        # Save frame
        output_path = os.path.join(non_temporal_dir, f"{timestamp:.2f}.pt")
        torch.save(data, output_path)
        
        # Collect statistics
        stats['temporal_window_size'] = 1
        frame_stats.append(stats)
        
        # Store processed frame for temporal processing
        processed_frames.append((data, voxel_df, stats))
    
    # Process temporal frames
    for window_size in [3, 5]:
        logger.info(f"Processing temporal frames with window size {window_size}...")
        
        # Create directory for temporal frames
        temporal_dir = os.path.join(output_dir, f'temporal_{window_size}')
        os.makedirs(temporal_dir, exist_ok=True)
        
        # Create visualization directory for temporal frames
        temporal_vis_dir = os.path.join(vis_dir, f'temporal_{window_size}') if vis_dir else None
        if temporal_vis_dir:
            os.makedirs(temporal_vis_dir, exist_ok=True)
        
        # Process each temporal window
        for i in tqdm(range(len(processed_frames) - window_size + 1), desc=f"Processing temporal windows (size={window_size})"):
            # Get frames for this window
            window_frames = processed_frames[i:i+window_size]
            
            # Create temporal frame
            temporal_data, temporal_stats = create_temporal_frame(
                window_frames, window_size, k_neighbors,
                visualize=visualize, vis_dir=temporal_vis_dir
            )
            
            if temporal_data is not None:
                # Save frame
                center_timestamp = window_frames[window_size//2][0].timestamp
                output_path = os.path.join(temporal_dir, f"{center_timestamp:.2f}.pt")
                torch.save(temporal_data, output_path)
                
                # Collect statistics
                frame_stats.append(temporal_stats)
    
    logger.info(f"Processed {len(timestamps)} frames with temporal windows")
    return frame_stats

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Convert annotated CSV files to GNN frames')
    parser.add_argument('--input_dir', type=str, required=True, help='Directory containing annotated CSV files')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save GNN frames')
    parser.add_argument('--voxel_size', type=float, default=0.1, help='Size of voxels')
    parser.add_argument('--k_neighbors', type=int, default=6, help='Number of neighbors for edge connections')
    parser.add_argument('--visualize', action='store_true', help='Visualize frames')
    args = parser.parse_args()
    
    # Find all CSV files
    csv_files = []
    for root, _, files in os.walk(args.input_dir):
        for file in files:
            if file.endswith('.csv') and file.startswith('annotated_'):
                csv_files.append(os.path.join(root, file))
    
    logger.info(f"Found {len(csv_files)} CSV files")
    
    # Process each CSV file
    all_stats = []
    for csv_file in csv_files:
        # Create output directory structure
        rel_path = os.path.relpath(os.path.dirname(csv_file), args.input_dir)
        output_dir = os.path.join(args.output_dir, rel_path)
        
        # Process CSV file
        stats = process_csv_file(
            csv_file, output_dir, 
            args.voxel_size, args.k_neighbors, 
            args.visualize
        )
        all_stats.extend(stats)
    
    # Save statistics
    stats_df = pd.DataFrame(all_stats)
    stats_df.to_csv(os.path.join(args.output_dir, 'frame_statistics.csv'), index=False)
    
    # Create summary statistics
    summary = {
        'total_frames': len(stats_df),
        'total_nodes': stats_df['num_nodes'].sum(),
        'total_edges': stats_df['num_edges'].sum(),
        'avg_nodes_per_frame': stats_df['num_nodes'].mean(),
        'avg_edges_per_frame': stats_df['num_edges'].mean(),
        'label_distribution': defaultdict(int)
    }
    
    # Aggregate label distribution
    for _, row in stats_df.iterrows():
        for label, count in row['label_distribution'].items():
            summary['label_distribution'][label] += count
    
    # Save summary
    with open(os.path.join(args.output_dir, 'summary.txt'), 'w') as f:
        f.write("GNN Frame Conversion Summary\n")
        f.write("===========================\n\n")
        f.write(f"Total frames: {summary['total_frames']}\n")
        f.write(f"Total nodes: {summary['total_nodes']}\n")
        f.write(f"Total edges: {summary['total_edges']}\n")
        f.write(f"Average nodes per frame: {summary['avg_nodes_per_frame']:.2f}\n")
        f.write(f"Average edges per frame: {summary['avg_edges_per_frame']:.2f}\n\n")
        f.write("Label distribution:\n")
        for label, count in summary['label_distribution'].items():
            label_name = list(LABEL_MAPPING.keys())[list(LABEL_MAPPING.values()).index(int(label))]
            f.write(f"  {label_name}: {count} ({count/summary['total_nodes']*100:.2f}%)\n")
    
    logger.info(f"Processed {len(csv_files)} CSV files with {len(all_stats)} frames")
    logger.info(f"Results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
