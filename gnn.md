# RoboFUSE Dataset Collaborative Perception Framework - Thesis Summary

## Overview

This thesis presents a comprehensive framework for collaborative perception using the RoboFUSE dataset, which involves multi-robot radar-based sensing combined with Vicon motion capture ground truth data. The framework processes point cloud data from multiple robots to enable collaborative perception in industrial environments.

## Dataset Structure

The dataset is organized in a hierarchical pipeline structure under `/home/<USER>/draft/data`:

### Data Pipeline Stages

1. **01_raw/** - Raw extracted data
   - `radar/` - Raw radar point cloud data from robots
   - `vicon/` - Vicon motion capture ground truth data
   - `06_gnn_frames/` - Raw GNN frame data

2. **02_synchronized/** - Time-synchronized multi-robot data
   - Contains synchronized radar and Vicon data across scenarios
   - Multiple scenario folders (CPPS_Diagonal, CPPS_Horizontal, etc.)

3. **03_transformed/** - Coordinate system transformations
   - Global coordinate frame transformations using Vicon data

4. **04_clean/** - Cleaned and filtered point cloud data
   - Boundary-corrected radar data with noise removal

5. **05_annotated/** - Semantically annotated point clouds
   - Labels: workstations, robots, boundaries, unknown

6. **06_gnn_frames/** - Graph Neural Network formatted data
   - Point clouds converted to graph representations

7. **07_gnn_ready/** - Final GNN training data
   - Temporal sequences and spatial relationships preserved

## Scenarios Covered

The framework processes multiple collaborative perception scenarios:
- **CPPS_Diagonal** - Diagonal robot movement patterns
- **CPPS_Diagonal_Horizontal** - Combined movement patterns  
- **CPPS_Horizontal** - Horizontal robot trajectories
- **CPPS_Horizontal_Diagonal** - Mixed trajectory scenarios

## Technical Framework

### Core Processing Pipeline

The framework consists of several interconnected processing stages implemented in `/home/<USER>/draft/scripts`:

#### 1. Data Extraction (`extract_data.py`, `extract_radar_data.py`, `extract_vicon_data.py`)
- Extracts radar point clouds and Vicon motion capture data from ROS bags
- Handles multiple robot configurations (ep03, ep05)
- Processes workstation position data (AS_1 through AS_6)

#### 2. Preprocessing & Synchronization (`preprocessingsync.py`)
- **Temporal Alignment**: Synchronizes radar data from multiple robots with Vicon ground truth
- **Movement Detection**: Automatically detects robot movement start times
- **Data Filtering**: Removes pre-movement static data
- **Resampling**: Standardizes temporal sampling rates (25ms intervals)
- **Gap Handling**: Interpolates missing data points within acceptable thresholds

#### 3. Coordinate Transformation (`coordinate_transformation.py`)
- **Global Frame Conversion**: Transforms local robot coordinates to global warehouse frame
- **2D/3D Transformations**: Supports both planar and full 3D transformations
- **Rotation Matrices**: Applies roll, pitch, yaw transformations using Vicon pose data
- **Validation Metrics**: Calculates inter-robot distances for transformation verification

#### 4. Data Cleaning (`radar_data_cleaner_correct_boundary.py`)
- **Boundary Correction**: Removes radar artifacts at warehouse boundaries
- **Noise Filtering**: Statistical outlier removal
- **Quality Assessment**: Maintains data integrity metrics
- **Visualization**: Generates before/after cleaning comparisons

#### 5. Semantic Annotation (`annotate_point_clouds.py`)
- **Workstation Detection**: Identifies points near assembly stations (AS_1-AS_6)
- **Robot Identification**: Labels points corresponding to other robots
- **Boundary Classification**: Marks warehouse boundary reflections
- **Unknown Category**: Handles unclassified points
- **Geometric Reasoning**: Uses rotated rectangle collision detection for workstations

#### 6. GNN Conversion (`direct_annotated_to_gnn.py`)
- **Graph Construction**: Creates nodes from point clouds with spatial relationships
- **Edge Generation**: Builds k-nearest neighbor graphs with distance-based connections
- **Temporal Windows**: Incorporates temporal context (1, 3, 5 frame windows)
- **Label Mapping**: Converts semantic annotations to numerical labels
- **Voxelization**: Applies spatial discretization for computational efficiency

#### 7. Evaluation & Validation (`evaluate_accuracy.py`)
- **RMSE Calculation**: Root mean square error for coordinate transformations
- **IoU Metrics**: Intersection over Union for spatial overlap assessment
- **Point Cloud Metrics**: Precision, recall, F1-score for registration quality
- **Error Heatmaps**: Spatial distribution of transformation errors
- **Statistical Analysis**: Comprehensive accuracy reporting

### Pipeline Orchestration

#### Main Pipeline (`collaborative_perception_pipeline.py`)
- **End-to-End Processing**: Coordinates all pipeline stages
- **Configuration Management**: Handles parameter settings and file paths
- **Error Handling**: Robust failure recovery and logging
- **Results Summarization**: Generates comprehensive processing reports

#### Batch Processing (`process_all.py`, `run_pipeline.py`)
- **Multi-Scenario Processing**: Handles multiple datasets automatically
- **Resource Management**: Optimizes processing for large datasets
- **Progress Monitoring**: Tracks processing status across scenarios

### Visualization & Analysis

#### Trajectory Analysis (`trajectory_generator.py`)
- **Robot Path Visualization**: 2D trajectory plots with workstation layouts
- **Temporal Analysis**: Time-series robot position tracking
- **Warehouse Layout**: Industrial environment context with assembly stations

#### Interactive Visualization (`vicon_interactive.py`)
- **Real-time Playback**: Interactive timeline controls for data exploration
- **Multi-Robot Tracking**: Simultaneous visualization of multiple robots
- **Workstation Integration**: Live assembly station position updates

#### Results Visualization (`visualize_results.py`)
- **Point Cloud Rendering**: 2D/3D point cloud visualizations
- **Annotation Display**: Color-coded semantic label visualization
- **Animation Generation**: Temporal sequence animations
- **Evaluation Plots**: Accuracy and performance metric visualization

## Key Contributions

### 1. Multi-Robot Synchronization Framework
- Robust temporal alignment across heterogeneous sensor systems
- Automatic movement detection for data filtering
- Scalable to multiple robot configurations

### 2. Collaborative Coordinate Transformation
- Unified global coordinate system for multi-robot perception
- Vicon-based ground truth integration
- Validation metrics for transformation accuracy

### 3. Semantic Scene Understanding
- Automated workstation detection and classification
- Industrial environment-specific annotation scheme
- Geometric reasoning for spatial relationship understanding

### 4. Graph-Based Representation Learning
- Point cloud to graph conversion for GNN compatibility
- Temporal context preservation in graph structures
- Scalable spatial relationship encoding

### 5. Comprehensive Evaluation Framework
- Multi-metric accuracy assessment
- Spatial error analysis and visualization
- Performance benchmarking across scenarios

## Technical Specifications

### Hardware Configuration
- **Robots**: ep03, ep05 mobile platforms
- **Sensors**: TI radar sensors for point cloud generation
- **Ground Truth**: Vicon motion capture system
- **Environment**: Industrial warehouse with 6 assembly stations

### Software Architecture
- **Language**: Python 3.x with scientific computing stack
- **Key Libraries**: pandas, numpy, matplotlib, torch-geometric
- **Data Formats**: CSV for tabular data, PyTorch tensors for GNN frames
- **Visualization**: matplotlib, interactive plotting capabilities

### Performance Metrics
- **Temporal Accuracy**: Sub-25ms synchronization precision
- **Spatial Accuracy**: RMSE measurements for coordinate transformations
- **Processing Efficiency**: Batch processing capabilities for large datasets
- **Scalability**: Support for multiple robots and extended scenarios

## Applications

This framework enables several collaborative perception applications:

1. **Multi-Robot SLAM**: Simultaneous localization and mapping with multiple robots
2. **Collaborative Object Detection**: Shared perception across robot teams
3. **Industrial Automation**: Warehouse and manufacturing environment perception
4. **Sensor Fusion**: Integration of radar and motion capture modalities
5. **Graph Neural Networks**: Spatial-temporal learning on point cloud graphs

## Future Directions

The framework provides a foundation for advancing collaborative perception research through:
- Integration of additional sensor modalities
- Real-time processing optimizations
- Extended multi-robot configurations
- Advanced graph neural network architectures
- Industrial deployment and validation

This comprehensive framework demonstrates the potential for collaborative perception systems in real-world industrial environments, providing both the technical infrastructure and evaluation methodology necessary for advancing multi-robot perception research.
